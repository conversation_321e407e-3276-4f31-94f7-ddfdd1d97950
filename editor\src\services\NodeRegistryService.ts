/**
 * 节点注册服务
 * 负责管理和注册可视化脚本节点
 */

import { EventEmitter } from 'events';

/**
 * 节点分类枚举
 */
export enum NodeCategory {
  EVENTS = 'events',
  FLOW = 'flow',
  LOGIC = 'logic',
  MATH = 'math',
  STRING = 'string',
  DEBUG = 'debug',
  ENTITY = 'entity',
  TRANSFORM = 'transform',
  PHYSICS = 'physics',
  ANIMATION = 'animation',
  AUDIO = 'audio',
  INPUT = 'input',
  UI = 'ui',
  RENDERING = 'rendering',
  CUSTOM = 'custom'
}

/**
 * 节点信息接口
 */
export interface NodeInfo {
  type: string;
  label: string;
  description: string;
  category: NodeCategory;
  icon: string;
  color: string;
  tags: string[];
  constructor?: any;
  metadata?: any;
}

/**
 * 节点注册服务类
 */
export class NodeRegistryService extends EventEmitter {
  private static instance: NodeRegistryService | null = null;
  private registeredNodes: Map<string, NodeInfo> = new Map();
  private nodesByCategory: Map<NodeCategory, NodeInfo[]> = new Map();
  private nodesByTag: Map<string, NodeInfo[]> = new Map();

  /**
   * 获取单例实例
   */
  public static getInstance(): NodeRegistryService {
    if (!NodeRegistryService.instance) {
      NodeRegistryService.instance = new NodeRegistryService();
    }
    return NodeRegistryService.instance;
  }

  /**
   * 私有构造函数
   */
  private constructor() {
    super();
    this.initializeDefaultNodes();
    this.initializeBatch4Nodes();
    this.initializeBatch5Nodes();
    this.initializeBatch7Nodes();
    this.initializeBatch8Nodes();
  }

  /**
   * 初始化默认节点
   */
  private initializeDefaultNodes(): void {
    // 核心事件节点
    this.registerNode({
      type: 'core/events/onStart',
      label: '开始事件',
      description: '当视觉脚本开始执行时触发',
      category: NodeCategory.EVENTS,
      icon: 'play',
      color: '#52c41a',
      tags: ['事件', '生命周期']
    });

    this.registerNode({
      type: 'core/events/onUpdate',
      label: '更新事件',
      description: '每帧更新时触发',
      category: NodeCategory.EVENTS,
      icon: 'sync',
      color: '#1890ff',
      tags: ['事件', '生命周期']
    });

    // 调试节点
    this.registerNode({
      type: 'core/debug/print',
      label: '打印',
      description: '在控制台打印日志',
      category: NodeCategory.DEBUG,
      icon: 'console',
      color: '#722ed1',
      tags: ['调试', '输出']
    });

    // 数学节点
    this.registerNode({
      type: 'math/basic/add',
      label: '加法',
      description: '计算两个数的和',
      category: NodeCategory.MATH,
      icon: 'plus',
      color: '#fa8c16',
      tags: ['数学', '运算']
    });

    // 流程控制节点
    this.registerNode({
      type: 'core/flow/delay',
      label: '延迟',
      description: '延迟指定时间后执行',
      category: NodeCategory.FLOW,
      icon: 'clock',
      color: '#eb2f96',
      tags: ['流程', '时间']
    });
  }

  /**
   * 初始化第4批次节点（渲染相机节点）
   */
  private initializeBatch4Nodes(): void {
    // 118. 创建透视相机节点
    this.registerNode({
      type: 'rendering/camera/createPerspectiveCamera',
      label: '创建透视相机',
      description: '创建透视投影相机',
      category: NodeCategory.RENDERING,
      icon: 'camera',
      color: '#FF5722',
      tags: ['渲染', '相机', '透视', '3D']
    });

    // 119. 创建正交相机节点
    this.registerNode({
      type: 'rendering/camera/createOrthographicCamera',
      label: '创建正交相机',
      description: '创建正交投影相机',
      category: NodeCategory.RENDERING,
      icon: 'camera',
      color: '#FF5722',
      tags: ['渲染', '相机', '正交', '2D']
    });

    // 120. 设置相机位置节点
    this.registerNode({
      type: 'rendering/camera/setCameraPosition',
      label: '设置相机位置',
      description: '设置相机在3D空间的位置',
      category: NodeCategory.RENDERING,
      icon: 'camera',
      color: '#FF5722',
      tags: ['渲染', '相机', '位置', '变换']
    });
  }

  /**
   * 注册节点
   * @param nodeInfo 节点信息
   */
  public registerNode(nodeInfo: NodeInfo): void {
    // 检查是否已存在
    if (this.registeredNodes.has(nodeInfo.type)) {
      console.warn(`节点类型已存在: ${nodeInfo.type}`);
      return;
    }

    // 注册节点
    this.registeredNodes.set(nodeInfo.type, nodeInfo);

    // 添加到分类映射
    if (!this.nodesByCategory.has(nodeInfo.category)) {
      this.nodesByCategory.set(nodeInfo.category, []);
    }
    this.nodesByCategory.get(nodeInfo.category)!.push(nodeInfo);

    // 添加到标签映射
    for (const tag of nodeInfo.tags) {
      if (!this.nodesByTag.has(tag)) {
        this.nodesByTag.set(tag, []);
      }
      this.nodesByTag.get(tag)!.push(nodeInfo);
    }

    // 触发注册事件
    this.emit('nodeRegistered', nodeInfo);
  }

  /**
   * 获取所有节点
   */
  public getAllNodes(): NodeInfo[] {
    return Array.from(this.registeredNodes.values());
  }

  /**
   * 根据类型获取节点
   * @param type 节点类型
   */
  public getNode(type: string): NodeInfo | undefined {
    return this.registeredNodes.get(type);
  }

  /**
   * 根据分类获取节点
   * @param category 节点分类
   */
  public getNodesByCategory(category: NodeCategory): NodeInfo[] {
    return this.nodesByCategory.get(category) || [];
  }

  /**
   * 根据标签获取节点
   * @param tag 标签
   */
  public getNodesByTag(tag: string): NodeInfo[] {
    return this.nodesByTag.get(tag) || [];
  }

  /**
   * 搜索节点
   * @param query 搜索关键词
   */
  public searchNodes(query: string): NodeInfo[] {
    const lowerQuery = query.toLowerCase();
    return this.getAllNodes().filter(node => 
      node.label.toLowerCase().includes(lowerQuery) ||
      node.description.toLowerCase().includes(lowerQuery) ||
      node.tags.some(tag => tag.toLowerCase().includes(lowerQuery))
    );
  }

  /**
   * 获取所有分类
   */
  public getAllCategories(): NodeCategory[] {
    return Array.from(this.nodesByCategory.keys());
  }

  /**
   * 获取所有标签
   */
  public getAllTags(): string[] {
    return Array.from(this.nodesByTag.keys());
  }

  /**
   * 注销节点
   * @param type 节点类型
   */
  public unregisterNode(type: string): boolean {
    const nodeInfo = this.registeredNodes.get(type);
    if (!nodeInfo) {
      return false;
    }

    // 从主映射中移除
    this.registeredNodes.delete(type);

    // 从分类映射中移除
    const categoryNodes = this.nodesByCategory.get(nodeInfo.category);
    if (categoryNodes) {
      const index = categoryNodes.findIndex(n => n.type === type);
      if (index !== -1) {
        categoryNodes.splice(index, 1);
      }
    }

    // 从标签映射中移除
    for (const tag of nodeInfo.tags) {
      const tagNodes = this.nodesByTag.get(tag);
      if (tagNodes) {
        const index = tagNodes.findIndex(n => n.type === type);
        if (index !== -1) {
          tagNodes.splice(index, 1);
        }
      }
    }

    // 触发注销事件
    this.emit('nodeUnregistered', nodeInfo);

    return true;
  }

  /**
   * 清空所有节点
   */
  public clear(): void {
    this.registeredNodes.clear();
    this.nodesByCategory.clear();
    this.nodesByTag.clear();
    this.emit('cleared');
  }

  /**
   * 获取节点统计信息
   */
  public getStats(): {
    totalNodes: number;
    categoryCounts: Record<string, number>;
    tagCounts: Record<string, number>;
  } {
    const categoryCounts: Record<string, number> = {};
    const tagCounts: Record<string, number> = {};

    for (const [category, nodes] of this.nodesByCategory.entries()) {
      categoryCounts[category] = nodes.length;
    }

    for (const [tag, nodes] of this.nodesByTag.entries()) {
      tagCounts[tag] = nodes.length;
    }

    return {
      totalNodes: this.registeredNodes.size,
      categoryCounts,
      tagCounts
    };
  }

  /**
   * 获取节点统计信息（别名方法）
   */
  public getNodeStatistics(): {
    totalNodes: number;
    categoryCounts: Record<string, number>;
    tagCounts: Record<string, number>;
  } {
    return this.getStats();
  }

  /**
   * 初始化第5批次节点：渲染系统核心（节点121-150）
   */
  private initializeBatch5Nodes(): void {
    // 相机控制节点
    this.registerNode({
      type: 'rendering/camera/setCameraTarget',
      label: '设置相机目标',
      description: '设置相机观察目标点',
      category: NodeCategory.RENDERING,
      icon: 'camera-target',
      color: '#FF5722',
      tags: ['渲染', '相机', '目标']
    });

    this.registerNode({
      type: 'rendering/camera/setCameraFOV',
      label: '设置相机视野',
      description: '设置相机视野角度',
      category: NodeCategory.RENDERING,
      icon: 'camera-fov',
      color: '#FF5722',
      tags: ['渲染', '相机', '视野']
    });

    // 光照系统节点
    this.registerNode({
      type: 'rendering/light/createDirectionalLight',
      label: '创建方向光',
      description: '创建平行光源',
      category: NodeCategory.RENDERING,
      icon: 'directional-light',
      color: '#FFC107',
      tags: ['渲染', '光照', '方向光']
    });

    this.registerNode({
      type: 'rendering/light/createPointLight',
      label: '创建点光源',
      description: '创建点状光源',
      category: NodeCategory.RENDERING,
      icon: 'point-light',
      color: '#FFC107',
      tags: ['渲染', '光照', '点光源']
    });

    this.registerNode({
      type: 'rendering/light/createSpotLight',
      label: '创建聚光灯',
      description: '创建锥形光源',
      category: NodeCategory.RENDERING,
      icon: 'spot-light',
      color: '#FFC107',
      tags: ['渲染', '光照', '聚光灯']
    });

    this.registerNode({
      type: 'rendering/light/createAmbientLight',
      label: '创建环境光',
      description: '创建全局环境光',
      category: NodeCategory.RENDERING,
      icon: 'ambient-light',
      color: '#FFC107',
      tags: ['渲染', '光照', '环境光']
    });

    this.registerNode({
      type: 'rendering/light/setLightColor',
      label: '设置光源颜色',
      description: '设置光源的颜色属性',
      category: NodeCategory.RENDERING,
      icon: 'light-color',
      color: '#FFC107',
      tags: ['渲染', '光照', '颜色']
    });

    this.registerNode({
      type: 'rendering/light/setLightIntensity',
      label: '设置光源强度',
      description: '设置光源的亮度强度',
      category: NodeCategory.RENDERING,
      icon: 'light-intensity',
      color: '#FFC107',
      tags: ['渲染', '光照', '强度']
    });

    // 阴影系统节点
    this.registerNode({
      type: 'rendering/shadow/enableShadows',
      label: '启用阴影',
      description: '启用实时阴影渲染',
      category: NodeCategory.RENDERING,
      icon: 'shadow',
      color: '#9C27B0',
      tags: ['渲染', '阴影', '启用']
    });

    this.registerNode({
      type: 'rendering/shadow/setShadowMapSize',
      label: '设置阴影贴图大小',
      description: '设置阴影质量',
      category: NodeCategory.RENDERING,
      icon: 'shadow-map',
      color: '#9C27B0',
      tags: ['渲染', '阴影', '质量']
    });

    // 材质系统节点
    this.registerNode({
      type: 'rendering/material/createBasicMaterial',
      label: '创建基础材质',
      description: '创建简单材质',
      category: NodeCategory.RENDERING,
      icon: 'material-basic',
      color: '#607D8B',
      tags: ['渲染', '材质', '基础']
    });

    this.registerNode({
      type: 'rendering/material/createStandardMaterial',
      label: '创建标准材质',
      description: '创建PBR材质',
      category: NodeCategory.RENDERING,
      icon: 'material-standard',
      color: '#607D8B',
      tags: ['渲染', '材质', 'PBR']
    });

    this.registerNode({
      type: 'rendering/material/createPhysicalMaterial',
      label: '创建物理材质',
      description: '创建物理基础材质',
      category: NodeCategory.RENDERING,
      icon: 'material-physical',
      color: '#607D8B',
      tags: ['渲染', '材质', '物理']
    });

    this.registerNode({
      type: 'rendering/material/setMaterialColor',
      label: '设置材质颜色',
      description: '设置材质的基础颜色',
      category: NodeCategory.RENDERING,
      icon: 'material-color',
      color: '#607D8B',
      tags: ['渲染', '材质', '颜色']
    });

    this.registerNode({
      type: 'rendering/material/setMaterialTexture',
      label: '设置材质纹理',
      description: '设置材质的纹理贴图',
      category: NodeCategory.RENDERING,
      icon: 'material-texture',
      color: '#607D8B',
      tags: ['渲染', '材质', '纹理']
    });

    this.registerNode({
      type: 'rendering/material/setMaterialOpacity',
      label: '设置材质透明度',
      description: '设置材质的透明程度',
      category: NodeCategory.RENDERING,
      icon: 'material-opacity',
      color: '#607D8B',
      tags: ['渲染', '材质', '透明度']
    });

    // 后处理节点
    this.registerNode({
      type: 'rendering/postprocess/enableFXAA',
      label: '启用抗锯齿',
      description: '启用FXAA抗锯齿',
      category: NodeCategory.RENDERING,
      icon: 'anti-aliasing',
      color: '#795548',
      tags: ['渲染', '后处理', '抗锯齿']
    });

    this.registerNode({
      type: 'rendering/postprocess/enableSSAO',
      label: '启用环境光遮蔽',
      description: '启用屏幕空间环境光遮蔽',
      category: NodeCategory.RENDERING,
      icon: 'ssao',
      color: '#795548',
      tags: ['渲染', '后处理', 'SSAO']
    });

    this.registerNode({
      type: 'rendering/postprocess/enableBloom',
      label: '启用辉光效果',
      description: '启用Bloom后处理',
      category: NodeCategory.RENDERING,
      icon: 'bloom',
      color: '#795548',
      tags: ['渲染', '后处理', '辉光']
    });

    // LOD系统节点
    this.registerNode({
      type: 'rendering/lod/setLODLevel',
      label: '设置LOD级别',
      description: '设置细节层次级别',
      category: NodeCategory.RENDERING,
      icon: 'lod',
      color: '#FF9800',
      tags: ['渲染', 'LOD', '优化']
    });

    // 物理刚体节点
    this.registerNode({
      type: 'physics/rigidbody/createRigidBody',
      label: '创建刚体',
      description: '创建物理刚体对象',
      category: NodeCategory.PHYSICS,
      icon: 'rigid-body',
      color: '#E91E63',
      tags: ['物理', '刚体', '创建']
    });

    this.registerNode({
      type: 'physics/rigidbody/setMass',
      label: '设置质量',
      description: '设置刚体质量',
      category: NodeCategory.PHYSICS,
      icon: 'mass',
      color: '#E91E63',
      tags: ['物理', '刚体', '质量']
    });

    this.registerNode({
      type: 'physics/rigidbody/setFriction',
      label: '设置摩擦力',
      description: '设置表面摩擦系数',
      category: NodeCategory.PHYSICS,
      icon: 'friction',
      color: '#E91E63',
      tags: ['物理', '刚体', '摩擦力']
    });

    this.registerNode({
      type: 'physics/rigidbody/setRestitution',
      label: '设置弹性',
      description: '设置碰撞弹性系数',
      category: NodeCategory.PHYSICS,
      icon: 'restitution',
      color: '#E91E63',
      tags: ['物理', '刚体', '弹性']
    });
  }

  /**
   * 初始化第7批次节点：动画系统扩展（节点181-210）
   */
  private initializeBatch7Nodes(): void {
    // 动画曲线节点（181-185）
    this.registerNode({
      type: 'animation/curve/evaluateCurve',
      label: '计算曲线值',
      description: '计算动画曲线在指定时间的值',
      category: NodeCategory.ANIMATION,
      icon: 'curve',
      color: '#4CAF50',
      tags: ['动画', '曲线', '计算']
    });

    this.registerNode({
      type: 'animation/state/createStateMachine',
      label: '创建状态机',
      description: '创建动画状态机',
      category: NodeCategory.ANIMATION,
      icon: 'state-machine',
      color: '#4CAF50',
      tags: ['动画', '状态机', '创建']
    });

    this.registerNode({
      type: 'animation/state/addState',
      label: '添加状态',
      description: '向状态机添加动画状态',
      category: NodeCategory.ANIMATION,
      icon: 'state-add',
      color: '#4CAF50',
      tags: ['动画', '状态', '添加']
    });

    this.registerNode({
      type: 'animation/state/addTransition',
      label: '添加过渡',
      description: '在状态间添加过渡条件',
      category: NodeCategory.ANIMATION,
      icon: 'transition',
      color: '#4CAF50',
      tags: ['动画', '状态', '过渡']
    });

    this.registerNode({
      type: 'animation/state/setCurrentState',
      label: '设置当前状态',
      description: '切换到指定动画状态',
      category: NodeCategory.ANIMATION,
      icon: 'state-current',
      color: '#4CAF50',
      tags: ['动画', '状态', '切换']
    });

    // 高级音频系统节点（186-200）
    this.registerNode({
      type: 'audio/source/create3DAudioSource',
      label: '创建3D音频源',
      description: '创建空间音频源',
      category: NodeCategory.AUDIO,
      icon: 'audio-3d',
      color: '#FF9800',
      tags: ['音频', '3D', '空间']
    });

    this.registerNode({
      type: 'audio/source/setAudioPosition',
      label: '设置音频位置',
      description: '设置3D音频源位置',
      category: NodeCategory.AUDIO,
      icon: 'audio-position',
      color: '#FF9800',
      tags: ['音频', '位置', '3D']
    });

    this.registerNode({
      type: 'audio/source/setAudioVelocity',
      label: '设置音频速度',
      description: '设置音频源移动速度',
      category: NodeCategory.AUDIO,
      icon: 'audio-velocity',
      color: '#FF9800',
      tags: ['音频', '速度', '多普勒']
    });

    this.registerNode({
      type: 'audio/listener/setListenerPosition',
      label: '设置听者位置',
      description: '设置音频听者位置',
      category: NodeCategory.AUDIO,
      icon: 'listener-position',
      color: '#FF9800',
      tags: ['音频', '听者', '位置']
    });

    this.registerNode({
      type: 'audio/listener/setListenerOrientation',
      label: '设置听者朝向',
      description: '设置音频听者朝向',
      category: NodeCategory.AUDIO,
      icon: 'listener-orientation',
      color: '#FF9800',
      tags: ['音频', '听者', '朝向']
    });

    this.registerNode({
      type: 'audio/effect/createReverb',
      label: '创建混响效果',
      description: '创建音频混响处理器',
      category: NodeCategory.AUDIO,
      icon: 'reverb',
      color: '#FF9800',
      tags: ['音频', '效果', '混响']
    });

    this.registerNode({
      type: 'audio/effect/createEcho',
      label: '创建回声效果',
      description: '创建音频回声处理器',
      category: NodeCategory.AUDIO,
      icon: 'echo',
      color: '#FF9800',
      tags: ['音频', '效果', '回声']
    });

    this.registerNode({
      type: 'audio/effect/createFilter',
      label: '创建滤波器',
      description: '创建音频滤波处理器',
      category: NodeCategory.AUDIO,
      icon: 'filter',
      color: '#FF9800',
      tags: ['音频', '效果', '滤波器']
    });

    this.registerNode({
      type: 'audio/analysis/createAnalyzer',
      label: '创建音频分析器',
      description: '创建音频频谱分析器',
      category: NodeCategory.AUDIO,
      icon: 'analyzer',
      color: '#FF9800',
      tags: ['音频', '分析', '频谱']
    });

    this.registerNode({
      type: 'audio/analysis/getFrequencyData',
      label: '获取频率数据',
      description: '获取音频频谱数据',
      category: NodeCategory.AUDIO,
      icon: 'frequency',
      color: '#FF9800',
      tags: ['音频', '分析', '频率']
    });

    this.registerNode({
      type: 'audio/analysis/getWaveformData',
      label: '获取波形数据',
      description: '获取音频波形数据',
      category: NodeCategory.AUDIO,
      icon: 'waveform',
      color: '#FF9800',
      tags: ['音频', '分析', '波形']
    });

    this.registerNode({
      type: 'audio/streaming/createAudioStream',
      label: '创建音频流',
      description: '创建实时音频流',
      category: NodeCategory.AUDIO,
      icon: 'stream',
      color: '#FF9800',
      tags: ['音频', '流媒体', '实时']
    });

    this.registerNode({
      type: 'audio/streaming/connectStream',
      label: '连接音频流',
      description: '连接到音频流源',
      category: NodeCategory.AUDIO,
      icon: 'connect',
      color: '#FF9800',
      tags: ['音频', '流媒体', '连接']
    });

    this.registerNode({
      type: 'audio/recording/startRecording',
      label: '开始录音',
      description: '开始音频录制',
      category: NodeCategory.AUDIO,
      icon: 'record-start',
      color: '#FF9800',
      tags: ['音频', '录制', '开始']
    });

    this.registerNode({
      type: 'audio/recording/stopRecording',
      label: '停止录音',
      description: '停止音频录制',
      category: NodeCategory.AUDIO,
      icon: 'record-stop',
      color: '#FF9800',
      tags: ['音频', '录制', '停止']
    });

    // 场景管理系统节点（201-210）
    this.registerNode({
      type: 'scene/management/createScene',
      label: '创建场景',
      description: '创建新的3D场景',
      category: NodeCategory.ENTITY,
      icon: 'scene',
      color: '#2196F3',
      tags: ['场景', '管理', '创建']
    });

    this.registerNode({
      type: 'scene/management/loadScene',
      label: '加载场景',
      description: '从文件加载场景',
      category: NodeCategory.ENTITY,
      icon: 'scene-load',
      color: '#2196F3',
      tags: ['场景', '管理', '加载']
    });

    this.registerNode({
      type: 'scene/management/saveScene',
      label: '保存场景',
      description: '保存场景到文件',
      category: NodeCategory.ENTITY,
      icon: 'scene-save',
      color: '#2196F3',
      tags: ['场景', '管理', '保存']
    });

    this.registerNode({
      type: 'scene/management/switchScene',
      label: '切换场景',
      description: '切换到指定场景',
      category: NodeCategory.ENTITY,
      icon: 'scene-switch',
      color: '#2196F3',
      tags: ['场景', '管理', '切换']
    });

    this.registerNode({
      type: 'scene/management/addToScene',
      label: '添加到场景',
      description: '将对象添加到场景',
      category: NodeCategory.ENTITY,
      icon: 'scene-add',
      color: '#2196F3',
      tags: ['场景', '管理', '添加']
    });

    this.registerNode({
      type: 'scene/management/removeFromScene',
      label: '从场景移除',
      description: '从场景移除对象',
      category: NodeCategory.ENTITY,
      icon: 'scene-remove',
      color: '#2196F3',
      tags: ['场景', '管理', '移除']
    });

    this.registerNode({
      type: 'scene/culling/enableFrustumCulling',
      label: '启用视锥体剔除',
      description: '启用视锥体剔除优化',
      category: NodeCategory.ENTITY,
      icon: 'frustum-culling',
      color: '#2196F3',
      tags: ['场景', '优化', '剔除']
    });

    this.registerNode({
      type: 'scene/culling/enableOcclusionCulling',
      label: '启用遮挡剔除',
      description: '启用遮挡剔除优化',
      category: NodeCategory.ENTITY,
      icon: 'occlusion-culling',
      color: '#2196F3',
      tags: ['场景', '优化', '遮挡']
    });

    this.registerNode({
      type: 'scene/optimization/enableBatching',
      label: '启用批处理',
      description: '启用渲染批处理',
      category: NodeCategory.ENTITY,
      icon: 'batching',
      color: '#2196F3',
      tags: ['场景', '优化', '批处理']
    });

    this.registerNode({
      type: 'scene/optimization/enableInstancing',
      label: '启用实例化',
      description: '启用实例化渲染',
      category: NodeCategory.ENTITY,
      icon: 'instancing',
      color: '#2196F3',
      tags: ['场景', '优化', '实例化']
    });

    console.log('第7批次动画系统扩展节点注册完成：30个节点');
  }

  /**
   * 初始化第8批次节点：音频与粒子系统（节点211-240）
   */
  private initializeBatch8Nodes(): void {
    // 高级音频节点（211-215）- 场景环境音频
    this.registerNode({
      type: 'scene/skybox/setSkybox',
      label: '设置天空盒',
      description: '设置场景天空盒',
      category: NodeCategory.ENTITY,
      icon: 'skybox',
      color: '#87CEEB',
      tags: ['场景', '天空盒', '环境']
    });

    this.registerNode({
      type: 'scene/fog/enableFog',
      label: '启用雾效',
      description: '启用场景雾效果',
      category: NodeCategory.ENTITY,
      icon: 'fog',
      color: '#B0C4DE',
      tags: ['场景', '雾效', '视觉效果']
    });

    this.registerNode({
      type: 'scene/fog/setFogColor',
      label: '设置雾颜色',
      description: '设置雾的颜色',
      category: NodeCategory.ENTITY,
      icon: 'color-palette',
      color: '#B0C4DE',
      tags: ['场景', '雾效', '颜色']
    });

    this.registerNode({
      type: 'scene/fog/setFogDensity',
      label: '设置雾密度',
      description: '设置雾的浓度',
      category: NodeCategory.ENTITY,
      icon: 'density',
      color: '#B0C4DE',
      tags: ['场景', '雾效', '密度']
    });

    this.registerNode({
      type: 'scene/environment/setEnvironmentMap',
      label: '设置环境贴图',
      description: '设置IBL环境贴图',
      category: NodeCategory.ENTITY,
      icon: 'environment',
      color: '#32CD32',
      tags: ['场景', '环境', 'IBL']
    });

    // 粒子系统节点（216-230）
    this.registerNode({
      type: 'particles/system/createParticleSystem',
      label: '创建粒子系统',
      description: '创建粒子效果系统',
      category: NodeCategory.ENTITY,
      icon: 'particles',
      color: '#FF6347',
      tags: ['粒子', '特效', '系统']
    });

    this.registerNode({
      type: 'particles/emitter/createEmitter',
      label: '创建发射器',
      description: '创建粒子发射器',
      category: NodeCategory.ENTITY,
      icon: 'emitter',
      color: '#FF6347',
      tags: ['粒子', '发射器', '特效']
    });

    this.registerNode({
      type: 'particles/emitter/setEmissionRate',
      label: '设置发射速率',
      description: '设置粒子发射频率',
      category: NodeCategory.ENTITY,
      icon: 'rate',
      color: '#FF6347',
      tags: ['粒子', '发射', '速率']
    });

    this.registerNode({
      type: 'particles/emitter/setEmissionShape',
      label: '设置发射形状',
      description: '设置粒子发射形状',
      category: NodeCategory.ENTITY,
      icon: 'shape',
      color: '#FF6347',
      tags: ['粒子', '发射', '形状']
    });

    this.registerNode({
      type: 'particles/particle/setLifetime',
      label: '设置粒子寿命',
      description: '设置粒子存活时间',
      category: NodeCategory.ENTITY,
      icon: 'lifetime',
      color: '#FFA500',
      tags: ['粒子', '寿命', '时间']
    });

    this.registerNode({
      type: 'particles/particle/setVelocity',
      label: '设置粒子速度',
      description: '设置粒子初始速度',
      category: NodeCategory.ENTITY,
      icon: 'velocity',
      color: '#FFA500',
      tags: ['粒子', '速度', '运动']
    });

    this.registerNode({
      type: 'particles/particle/setSize',
      label: '设置粒子大小',
      description: '设置粒子尺寸',
      category: NodeCategory.ENTITY,
      icon: 'size',
      color: '#FFA500',
      tags: ['粒子', '大小', '尺寸']
    });

    this.registerNode({
      type: 'particles/particle/setColor',
      label: '设置粒子颜色',
      description: '设置粒子颜色',
      category: NodeCategory.ENTITY,
      icon: 'color',
      color: '#FFA500',
      tags: ['粒子', '颜色', '外观']
    });

    this.registerNode({
      type: 'particles/forces/addGravity',
      label: '添加重力',
      description: '为粒子添加重力影响',
      category: NodeCategory.ENTITY,
      icon: 'gravity',
      color: '#8A2BE2',
      tags: ['粒子', '重力', '物理']
    });

    this.registerNode({
      type: 'particles/forces/addWind',
      label: '添加风力',
      description: '为粒子添加风力影响',
      category: NodeCategory.ENTITY,
      icon: 'wind',
      color: '#8A2BE2',
      tags: ['粒子', '风力', '物理']
    });

    this.registerNode({
      type: 'particles/forces/addTurbulence',
      label: '添加湍流',
      description: '为粒子添加湍流效果',
      category: NodeCategory.ENTITY,
      icon: 'turbulence',
      color: '#8A2BE2',
      tags: ['粒子', '湍流', '物理']
    });

    this.registerNode({
      type: 'particles/collision/enableCollision',
      label: '启用粒子碰撞',
      description: '启用粒子与物体碰撞',
      category: NodeCategory.ENTITY,
      icon: 'collision',
      color: '#DC143C',
      tags: ['粒子', '碰撞', '物理']
    });

    this.registerNode({
      type: 'particles/material/setParticleMaterial',
      label: '设置粒子材质',
      description: '设置粒子渲染材质',
      category: NodeCategory.ENTITY,
      icon: 'material',
      color: '#4169E1',
      tags: ['粒子', '材质', '渲染']
    });

    this.registerNode({
      type: 'particles/animation/animateSize',
      label: '动画粒子大小',
      description: '创建粒子大小动画',
      category: NodeCategory.ANIMATION,
      icon: 'animate-size',
      color: '#FF1493',
      tags: ['粒子', '动画', '大小']
    });

    this.registerNode({
      type: 'particles/animation/animateColor',
      label: '动画粒子颜色',
      description: '创建粒子颜色动画',
      category: NodeCategory.ANIMATION,
      icon: 'animate-color',
      color: '#FF1493',
      tags: ['粒子', '动画', '颜色']
    });

    // 地形系统节点（231-232）- 部分实现
    this.registerNode({
      type: 'terrain/generation/createTerrain',
      label: '创建地形',
      description: '创建地形网格',
      category: NodeCategory.ENTITY,
      icon: 'terrain',
      color: '#8B4513',
      tags: ['地形', '生成', '网格']
    });

    this.registerNode({
      type: 'terrain/generation/generateHeightmap',
      label: '生成高度图',
      description: '生成地形高度图',
      category: NodeCategory.ENTITY,
      icon: 'heightmap',
      color: '#8B4513',
      tags: ['地形', '高度图', '生成']
    });

    console.log('第8批次音频与粒子系统节点注册完成：22个节点（211-232）');
  }
}

// 导出单例实例
export const nodeRegistryService = NodeRegistryService.getInstance();
