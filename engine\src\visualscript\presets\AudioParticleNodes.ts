/**
 * 音频与粒子系统节点
 * 第8批次：音频与粒子系统（节点211-240）
 * 简化版本 - 包含核心节点实现
 */

import * as THREE from 'three';
import type { Entity } from '../../core/Entity';
import { Node } from '../nodes/Node';
import { NodeCategory, SocketDirection, SocketType } from '../nodes/Node';

// ============================================================================
// 高级音频节点（211-215）- 场景环境音频
// ============================================================================

/**
 * 设置天空盒节点 (211)
 * 设置场景天空盒
 */
export class SetSkyboxNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'execute',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });

    this.addInput({
      name: 'scene',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '场景对象'
    });

    this.addInput({
      name: 'skyboxTexture',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '天空盒纹理'
    });

    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成'
    });

    this.addOutput({
      name: 'scene',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      description: '更新后的场景'
    });
  }

  public execute(): any {
    const scene = this.getInputValue('scene');
    const skyboxTexture = this.getInputValue('skyboxTexture');

    try {
      if (scene && skyboxTexture) {
        scene.background = skyboxTexture;
        scene.environment = skyboxTexture;
      }
      this.setOutputValue('scene', scene);
      this.triggerFlow('completed');
    } catch (error) {
      console.error('SetSkyboxNode: 设置天空盒失败', error);
      this.setOutputValue('scene', scene);
      this.triggerFlow('completed');
    }
  }
}

/**
 * 启用雾效节点 (212)
 * 启用场景雾效果
 */
export class EnableFogNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'execute',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });

    this.addInput({
      name: 'scene',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '场景对象'
    });

    this.addInput({
      name: 'color',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '雾颜色',
      defaultValue: 0xcccccc
    });

    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成'
    });

    this.addOutput({
      name: 'scene',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      description: '更新后的场景'
    });
  }

  public execute(): any {
    const scene = this.getInputValue('scene');
    const color = this.getInputValue('color');

    try {
      if (scene) {
        scene.fog = new THREE.Fog(color, 1, 1000);
      }
      this.setOutputValue('scene', scene);
      this.triggerFlow('completed');
    } catch (error) {
      console.error('EnableFogNode: 启用雾效失败', error);
      this.setOutputValue('scene', scene);
      this.triggerFlow('completed');
    }
  }
}

/**
 * 创建粒子系统节点 (216)
 * 创建粒子效果系统
 */
export class CreateParticleSystemNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'execute',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });

    this.addInput({
      name: 'maxParticles',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '最大粒子数',
      defaultValue: 1000
    });

    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成'
    });

    this.addOutput({
      name: 'particleSystem',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      description: '粒子系统'
    });
  }

  public execute(): any {
    const maxParticles = this.getInputValue('maxParticles');

    try {
      const geometry = new THREE.BufferGeometry();
      const positions = new Float32Array(maxParticles * 3);
      geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));

      const material = new THREE.PointsMaterial({
        size: 1,
        transparent: true,
        opacity: 0.8
      });

      const particleSystem = new THREE.Points(geometry, material);
      (particleSystem as any).maxParticles = maxParticles;

      this.setOutputValue('particleSystem', particleSystem);
      this.triggerFlow('completed');
    } catch (error) {
      console.error('CreateParticleSystemNode: 创建粒子系统失败', error);
      this.setOutputValue('particleSystem', null);
      this.triggerFlow('completed');
    }
  }
}

/**
 * 创建地形节点 (231)
 * 创建地形网格
 */
export class CreateTerrainNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'execute',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });

    this.addInput({
      name: 'width',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '地形宽度',
      defaultValue: 100
    });

    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成'
    });

    this.addOutput({
      name: 'terrain',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      description: '地形对象'
    });
  }

  public execute(): any {
    const width = this.getInputValue('width');

    try {
      const geometry = new THREE.PlaneGeometry(width, width, 64, 64);
      const material = new THREE.MeshLambertMaterial({ color: 0x8B7355 });
      const terrain = new THREE.Mesh(geometry, material);
      terrain.rotation.x = -Math.PI / 2;

      this.setOutputValue('terrain', terrain);
      this.triggerFlow('completed');
    } catch (error) {
      console.error('CreateTerrainNode: 创建地形失败', error);
      this.setOutputValue('terrain', null);
      this.triggerFlow('completed');
    }
  }
}

/**
 * 创建水面节点 (238)
 * 创建水体表面
 */
export class CreateWaterSurfaceNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    this.addInput({
      name: 'execute',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行'
    });

    this.addInput({
      name: 'size',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '水面大小',
      defaultValue: 100
    });

    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成'
    });

    this.addOutput({
      name: 'waterSurface',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      description: '水面对象'
    });
  }

  public execute(): any {
    const size = this.getInputValue('size');

    try {
      const geometry = new THREE.PlaneGeometry(size, size, 32, 32);
      const material = new THREE.MeshLambertMaterial({
        color: 0x006994,
        transparent: true,
        opacity: 0.7
      });

      const waterSurface = new THREE.Mesh(geometry, material);
      waterSurface.rotation.x = -Math.PI / 2;
      (waterSurface as any).isWater = true;

      this.setOutputValue('waterSurface', waterSurface);
      this.triggerFlow('completed');
    } catch (error) {
      console.error('CreateWaterSurfaceNode: 创建水面失败', error);
      this.setOutputValue('waterSurface', null);
      this.triggerFlow('completed');
    }
  }
}
