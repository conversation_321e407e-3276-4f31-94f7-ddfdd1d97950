/**
 * 音频与粒子系统节点 - 第三部分
 * 第8批次：音频与粒子系统（节点228-240）
 * 包含最后的粒子系统节点
 */

import * as THREE from 'three';
import type { Entity } from '../../core/Entity';
import { Node } from '../nodes/Node';
import { NodeCategory, SocketDirection, SocketType } from '../nodes/Node';

// ============================================================================
// 粒子系统节点（228-240）- 最后部分
// ============================================================================

/**
 * 设置粒子材质节点 (228)
 * 设置粒子渲染材质
 */
export class SetParticleMaterialNode extends Node {
  constructor() {
    super('particles/material/setParticleMaterial', '设置粒子材质', NodeCategory.ENTITY);

    this.addInput({
      name: 'particleSystem',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '粒子系统'
    });

    this.addInput({
      name: 'texture',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '粒子纹理'
    });

    this.addInput({
      name: 'blending',
      type: SocketType.STRING,
      direction: SocketDirection.INPUT,
      description: '混合模式',
      defaultValue: 'additive'
    });

    this.addOutput({
      name: 'particleSystem',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      description: '更新后的粒子系统'
    });
  }

  public execute(): any {
    const particleSystem = this.getInputValue('particleSystem');
    const texture = this.getInputValue('texture');
    const blending = this.getInputValue('blending');

    if (!particleSystem) {
      console.warn('SetParticleMaterialNode: 粒子系统为空');
      this.setOutputValue('particleSystem', null);
      this.triggerFlow('completed');
      return;
    }

    try {
      const material = particleSystem.material as THREE.PointsMaterial;
      
      if (texture) {
        material.map = texture;
      }

      // 设置混合模式
      switch (blending) {
        case 'additive':
          material.blending = THREE.AdditiveBlending;
          break;
        case 'multiply':
          material.blending = THREE.MultiplyBlending;
          break;
        case 'subtract':
          material.blending = THREE.SubtractiveBlending;
          break;
        default:
          material.blending = THREE.NormalBlending;
      }

      material.needsUpdate = true;
      this.setOutputValue('particleSystem', particleSystem);
      this.triggerFlow('completed');
    } catch (error) {
      console.error('SetParticleMaterialNode: 设置粒子材质失败', error);
      this.setOutputValue('particleSystem', particleSystem);
      this.triggerFlow('completed');
    }
  }
}

/**
 * 动画粒子大小节点 (229)
 * 创建粒子大小动画
 */
export class AnimateSizeNode extends Node {
  constructor() {
    super('particles/animation/animateSize', '动画粒子大小', NodeCategory.ENTITY);

    this.addInput({
      name: 'particleSystem',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '粒子系统'
    });

    this.addInput({
      name: 'startSize',
      type: SocketType.NUMBER,
      direction: SocketDirection.INPUT,
      description: '起始大小',
      defaultValue: 0.1
    });

    this.addInput({
      name: 'endSize',
      type: SocketType.NUMBER,
      direction: SocketDirection.INPUT,
      description: '结束大小',
      defaultValue: 2.0
    });

    this.addInput({
      name: 'duration',
      type: SocketType.NUMBER,
      direction: SocketDirection.INPUT,
      description: '动画时长',
      defaultValue: 1.0
    });

    this.addOutput({
      name: 'particleSystem',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      description: '更新后的粒子系统'
    });
  }

  public execute(): any {
    const particleSystem = this.getInputValue('particleSystem');
    const startSize = this.getInputValue('startSize');
    const endSize = this.getInputValue('endSize');
    const duration = this.getInputValue('duration');

    if (!particleSystem) {
      console.warn('AnimateSizeNode: 粒子系统为空');
      this.setOutputValue('particleSystem', null);
      this.triggerFlow('completed');
      return;
    }

    try {
      (particleSystem as any).sizeAnimation = {
        startSize: startSize,
        endSize: endSize,
        duration: duration,
        enabled: true
      };
      this.setOutputValue('particleSystem', particleSystem);
      this.triggerFlow('completed');
    } catch (error) {
      console.error('AnimateSizeNode: 动画粒子大小失败', error);
      this.setOutputValue('particleSystem', particleSystem);
      this.triggerFlow('completed');
    }
  }
}

/**
 * 动画粒子颜色节点 (230)
 * 创建粒子颜色动画
 */
export class AnimateColorNode extends Node {
  constructor() {
    super('particles/animation/animateColor', '动画粒子颜色', NodeCategory.ENTITY);

    this.addInput({
      name: 'particleSystem',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '粒子系统'
    });

    this.addInput({
      name: 'startColor',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '起始颜色',
      defaultValue: 0xffffff
    });

    this.addInput({
      name: 'endColor',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '结束颜色',
      defaultValue: 0x000000
    });

    this.addInput({
      name: 'duration',
      type: SocketType.NUMBER,
      direction: SocketDirection.INPUT,
      description: '动画时长',
      defaultValue: 1.0
    });

    this.addOutput({
      name: 'particleSystem',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      description: '更新后的粒子系统'
    });
  }

  public execute(): any {
    const particleSystem = this.getInputValue('particleSystem');
    const startColor = this.getInputValue('startColor');
    const endColor = this.getInputValue('endColor');
    const duration = this.getInputValue('duration');

    if (!particleSystem) {
      console.warn('AnimateColorNode: 粒子系统为空');
      this.setOutputValue('particleSystem', null);
      this.triggerFlow('completed');
      return;
    }

    try {
      (particleSystem as any).colorAnimation = {
        startColor: new THREE.Color(startColor),
        endColor: new THREE.Color(endColor),
        duration: duration,
        enabled: true
      };
      this.setOutputValue('particleSystem', particleSystem);
      this.triggerFlow('completed');
    } catch (error) {
      console.error('AnimateColorNode: 动画粒子颜色失败', error);
      this.setOutputValue('particleSystem', particleSystem);
      this.triggerFlow('completed');
    }
  }
}

/**
 * 创建地形节点 (231)
 * 创建地形网格
 */
export class CreateTerrainNode extends Node {
  constructor() {
    super('terrain/generation/createTerrain', '创建地形', NodeCategory.ENTITY);

    this.addInput({
      name: 'width',
      type: SocketType.NUMBER,
      direction: SocketDirection.INPUT,
      description: '地形宽度',
      defaultValue: 100
    });

    this.addInput({
      name: 'height',
      type: SocketType.NUMBER,
      direction: SocketDirection.INPUT,
      description: '地形高度',
      defaultValue: 100
    });

    this.addInput({
      name: 'segments',
      type: SocketType.NUMBER,
      direction: SocketDirection.INPUT,
      description: '分段数',
      defaultValue: 64
    });

    this.addOutput({
      name: 'terrain',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      description: '地形对象'
    });
  }

  public execute(): any {
    const width = this.getInputValue('width');
    const height = this.getInputValue('height');
    const segments = this.getInputValue('segments');

    try {
      // 创建地形几何体
      const geometry = new THREE.PlaneGeometry(width, height, segments, segments);
      
      // 创建基础材质
      const material = new THREE.MeshLambertMaterial({ 
        color: 0x8B7355,
        wireframe: false
      });

      // 创建地形网格
      const terrain = new THREE.Mesh(geometry, material);
      terrain.rotation.x = -Math.PI / 2; // 水平放置
      terrain.receiveShadow = true;

      // 添加地形属性
      (terrain as any).terrainWidth = width;
      (terrain as any).terrainHeight = height;
      (terrain as any).terrainSegments = segments;

      this.setOutputValue('terrain', terrain);
      this.triggerFlow('completed');
    } catch (error) {
      console.error('CreateTerrainNode: 创建地形失败', error);
      this.setOutputValue('terrain', null);
      this.triggerFlow('completed');
    }
  }
}

/**
 * 生成高度图节点 (232)
 * 生成地形高度图
 */
export class GenerateHeightmapNode extends Node {
  constructor() {
    super('terrain/generation/generateHeightmap', '生成高度图', NodeCategory.ENTITY);

    this.addInput({
      name: 'terrain',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '地形对象'
    });

    this.addInput({
      name: 'scale',
      type: SocketType.NUMBER,
      direction: SocketDirection.INPUT,
      description: '高度缩放',
      defaultValue: 10
    });

    this.addInput({
      name: 'octaves',
      type: SocketType.NUMBER,
      direction: SocketDirection.INPUT,
      description: '噪声层数',
      defaultValue: 4
    });

    this.addOutput({
      name: 'terrain',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      description: '更新后的地形'
    });
  }

  public execute(): any {
    const terrain = this.getInputValue('terrain');
    const scale = this.getInputValue('scale');
    const octaves = this.getInputValue('octaves');

    if (!terrain) {
      console.warn('GenerateHeightmapNode: 地形为空');
      this.setOutputValue('terrain', null);
      this.triggerFlow('completed');
      return;
    }

    try {
      const geometry = terrain.geometry as THREE.PlaneGeometry;
      const positions = geometry.attributes.position.array as Float32Array;

      // 简单的噪声生成（实际项目中应使用更复杂的噪声算法）
      for (let i = 0; i < positions.length; i += 3) {
        const x = positions[i];
        const z = positions[i + 2];
        
        let height = 0;
        let amplitude = 1;
        let frequency = 0.01;

        for (let j = 0; j < octaves; j++) {
          height += Math.sin(x * frequency) * Math.cos(z * frequency) * amplitude;
          amplitude *= 0.5;
          frequency *= 2;
        }

        positions[i + 1] = height * scale;
      }

      geometry.attributes.position.needsUpdate = true;
      geometry.computeVertexNormals();

      this.setOutputValue('terrain', terrain);
      this.triggerFlow('completed');
    } catch (error) {
      console.error('GenerateHeightmapNode: 生成高度图失败', error);
      this.setOutputValue('terrain', terrain);
      this.triggerFlow('completed');
    }
  }
}

/**
 * 应用噪声节点 (233)
 * 为地形应用噪声纹理
 */
export class ApplyNoiseNode extends Node {
  constructor() {
    super('terrain/generation/applyNoise', '应用噪声', NodeCategory.ENTITY);

    this.addInput({
      name: 'terrain',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '地形对象'
    });

    this.addInput({
      name: 'noiseScale',
      type: SocketType.NUMBER,
      direction: SocketDirection.INPUT,
      description: '噪声缩放',
      defaultValue: 0.1
    });

    this.addInput({
      name: 'amplitude',
      type: SocketType.NUMBER,
      direction: SocketDirection.INPUT,
      description: '振幅',
      defaultValue: 5
    });

    this.addOutput({
      name: 'terrain',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      description: '更新后的地形'
    });
  }

  public execute(): any {
    const terrain = this.getInputValue('terrain');
    const noiseScale = this.getInputValue('noiseScale');
    const amplitude = this.getInputValue('amplitude');

    if (!terrain) {
      console.warn('ApplyNoiseNode: 地形为空');
      this.setOutputValue('terrain', null);
      this.triggerFlow('completed');
      return;
    }

    try {
      const geometry = terrain.geometry as THREE.PlaneGeometry;
      const positions = geometry.attributes.position.array as Float32Array;

      // 应用噪声到地形高度
      for (let i = 0; i < positions.length; i += 3) {
        const x = positions[i];
        const z = positions[i + 2];

        // 简单的噪声函数
        const noise = Math.sin(x * noiseScale) * Math.cos(z * noiseScale);
        positions[i + 1] += noise * amplitude;
      }

      geometry.attributes.position.needsUpdate = true;
      geometry.computeVertexNormals();

      this.setOutputValue('terrain', terrain);
      this.triggerFlow('completed');
    } catch (error) {
      console.error('ApplyNoiseNode: 应用噪声失败', error);
      this.setOutputValue('terrain', terrain);
      this.triggerFlow('completed');
    }
  }
}

/**
 * 设置地形纹理节点 (234)
 * 设置地形表面纹理
 */
export class SetTerrainTextureNode extends Node {
  constructor() {
    super('terrain/texture/setTerrainTexture', '设置地形纹理', NodeCategory.ENTITY);

    this.addInput({
      name: 'terrain',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '地形对象'
    });

    this.addInput({
      name: 'texture',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '地形纹理'
    });

    this.addInput({
      name: 'repeat',
      type: SocketType.NUMBER,
      direction: SocketDirection.INPUT,
      description: '纹理重复次数',
      defaultValue: 10
    });

    this.addOutput({
      name: 'terrain',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      description: '更新后的地形'
    });
  }

  public execute(): any {
    const terrain = this.getInputValue('terrain');
    const texture = this.getInputValue('texture');
    const repeat = this.getInputValue('repeat');

    if (!terrain) {
      console.warn('SetTerrainTextureNode: 地形为空');
      this.setOutputValue('terrain', null);
      this.triggerFlow('completed');
      return;
    }

    try {
      const material = terrain.material as THREE.MeshLambertMaterial;

      if (texture) {
        material.map = texture;
        texture.wrapS = THREE.RepeatWrapping;
        texture.wrapT = THREE.RepeatWrapping;
        texture.repeat.set(repeat, repeat);
        material.needsUpdate = true;
      }

      this.setOutputValue('terrain', terrain);
      this.triggerFlow('completed');
    } catch (error) {
      console.error('SetTerrainTextureNode: 设置地形纹理失败', error);
      this.setOutputValue('terrain', terrain);
      this.triggerFlow('completed');
    }
  }
}

/**
 * 混合纹理节点 (235)
 * 混合多个地形纹理
 */
export class BlendTexturesNode extends Node {
  constructor() {
    super('terrain/texture/blendTextures', '混合纹理', NodeCategory.ENTITY);

    this.addInput({
      name: 'terrain',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '地形对象'
    });

    this.addInput({
      name: 'texture1',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '第一个纹理'
    });

    this.addInput({
      name: 'texture2',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '第二个纹理'
    });

    this.addInput({
      name: 'blendFactor',
      type: SocketType.NUMBER,
      direction: SocketDirection.INPUT,
      description: '混合因子',
      defaultValue: 0.5
    });

    this.addOutput({
      name: 'terrain',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      description: '更新后的地形'
    });
  }

  public execute(): any {
    const terrain = this.getInputValue('terrain');
    const texture1 = this.getInputValue('texture1');
    const texture2 = this.getInputValue('texture2');
    const blendFactor = this.getInputValue('blendFactor');

    if (!terrain) {
      console.warn('BlendTexturesNode: 地形为空');
      this.setOutputValue('terrain', null);
      this.triggerFlow('completed');
      return;
    }

    try {
      // 创建混合材质（简化实现）
      const material = new THREE.MeshLambertMaterial();

      if (texture1 && texture2) {
        // 这里应该使用着色器来实现真正的纹理混合
        // 简化实现：根据混合因子选择主要纹理
        const primaryTexture = blendFactor < 0.5 ? texture1 : texture2;
        material.map = primaryTexture;
      } else if (texture1) {
        material.map = texture1;
      } else if (texture2) {
        material.map = texture2;
      }

      terrain.material = material;
      this.setOutputValue('terrain', terrain);
      this.triggerFlow('completed');
    } catch (error) {
      console.error('BlendTexturesNode: 混合纹理失败', error);
      this.setOutputValue('terrain', terrain);
      this.triggerFlow('completed');
    }
  }
}
