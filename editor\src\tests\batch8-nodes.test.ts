/**
 * 第8批次节点功能测试
 * 测试音频与粒子系统节点的基本功能
 */

import { nodeRegistryService } from '../services/NodeRegistryService';

describe('第8批次节点测试 - 音频与粒子系统（节点211-240）', () => {
  beforeAll(() => {
    // 确保节点注册服务已初始化
    expect(nodeRegistryService).toBeDefined();
  });

  describe('节点注册测试', () => {
    test('应该注册所有高级音频节点', () => {
      const audioNodes = [
        'scene/skybox/setSkybox',
        'scene/fog/enableFog',
        'scene/fog/setFogColor',
        'scene/fog/setFogDensity',
        'scene/environment/setEnvironmentMap'
      ];

      audioNodes.forEach(nodeType => {
        const node = nodeRegistryService.getNode(nodeType);
        expect(node).toBeDefined();
        expect(node?.type).toBe(nodeType);
      });
    });

    test('应该注册所有粒子系统节点', () => {
      const particleNodes = [
        'particles/system/createParticleSystem',
        'particles/emitter/createEmitter',
        'particles/emitter/setEmissionRate',
        'particles/emitter/setEmissionShape',
        'particles/particle/setLifetime',
        'particles/particle/setVelocity',
        'particles/particle/setSize',
        'particles/particle/setColor',
        'particles/forces/addGravity',
        'particles/forces/addWind',
        'particles/forces/addTurbulence',
        'particles/collision/enableCollision',
        'particles/material/setParticleMaterial',
        'particles/animation/animateSize',
        'particles/animation/animateColor'
      ];

      particleNodes.forEach(nodeType => {
        const node = nodeRegistryService.getNode(nodeType);
        expect(node).toBeDefined();
        expect(node?.type).toBe(nodeType);
      });
    });

    test('应该注册地形和水体系统节点', () => {
      const terrainWaterNodes = [
        'terrain/generation/createTerrain',
        'terrain/generation/generateHeightmap',
        'terrain/generation/applyNoise',
        'terrain/texture/setTerrainTexture',
        'terrain/texture/blendTextures',
        'terrain/lod/enableTerrainLOD',
        'terrain/collision/enableTerrainCollision',
        'water/system/createWaterSurface',
        'water/waves/addWaves',
        'water/reflection/enableReflection'
      ];

      terrainWaterNodes.forEach(nodeType => {
        const node = nodeRegistryService.getNode(nodeType);
        expect(node).toBeDefined();
        expect(node?.type).toBe(nodeType);
      });
    });
  });

  describe('节点属性测试', () => {
    test('高级音频节点应该有正确的属性', () => {
      const skyboxNode = nodeRegistryService.getNode('scene/skybox/setSkybox');
      expect(skyboxNode).toBeDefined();
      expect(skyboxNode?.label).toBe('设置天空盒');
      expect(skyboxNode?.description).toBe('设置场景天空盒');
      expect(skyboxNode?.color).toBe('#87CEEB');
      expect(skyboxNode?.tags).toContain('场景');
      expect(skyboxNode?.tags).toContain('天空盒');
    });

    test('粒子系统节点应该有正确的属性', () => {
      const particleNode = nodeRegistryService.getNode('particles/system/createParticleSystem');
      expect(particleNode).toBeDefined();
      expect(particleNode?.label).toBe('创建粒子系统');
      expect(particleNode?.description).toBe('创建粒子效果系统');
      expect(particleNode?.color).toBe('#FF6347');
      expect(particleNode?.tags).toContain('粒子');
      expect(particleNode?.tags).toContain('特效');
    });

    test('地形节点应该有正确的属性', () => {
      const terrainNode = nodeRegistryService.getNode('terrain/generation/createTerrain');
      expect(terrainNode).toBeDefined();
      expect(terrainNode?.label).toBe('创建地形');
      expect(terrainNode?.description).toBe('创建地形网格');
      expect(terrainNode?.color).toBe('#8B4513');
      expect(terrainNode?.tags).toContain('地形');
      expect(terrainNode?.tags).toContain('生成');
    });
  });

  describe('节点分类测试', () => {
    test('应该正确分类到实体类别', () => {
      const entityNodes = nodeRegistryService.getNodesByCategory('entity' as any);
      const batch8EntityNodes = entityNodes.filter(node =>
        node.type.startsWith('scene/') ||
        node.type.startsWith('particles/') ||
        node.type.startsWith('terrain/') ||
        node.type.startsWith('water/')
      );
      expect(batch8EntityNodes.length).toBeGreaterThanOrEqual(28);
    });

    test('应该正确分类到动画类别', () => {
      const animationNodes = nodeRegistryService.getNodesByCategory('animation' as any);
      const batch8AnimationNodes = animationNodes.filter(node => 
        node.type.includes('particles/animation/')
      );
      expect(batch8AnimationNodes.length).toBeGreaterThanOrEqual(2);
    });
  });

  describe('节点搜索测试', () => {
    test('应该能够通过标签搜索场景节点', () => {
      const sceneNodes = nodeRegistryService.getNodesByTag('场景');
      const batch8SceneNodes = sceneNodes.filter(node => 
        node.type.startsWith('scene/')
      );
      expect(batch8SceneNodes.length).toBeGreaterThanOrEqual(5);
    });

    test('应该能够通过标签搜索粒子节点', () => {
      const particleNodes = nodeRegistryService.getNodesByTag('粒子');
      expect(particleNodes.length).toBeGreaterThanOrEqual(15);
    });

    test('应该能够通过标签搜索地形节点', () => {
      const terrainNodes = nodeRegistryService.getNodesByTag('地形');
      expect(terrainNodes.length).toBeGreaterThanOrEqual(7);
    });

    test('应该能够通过标签搜索水体节点', () => {
      const waterNodes = nodeRegistryService.getNodesByTag('水体');
      expect(waterNodes.length).toBeGreaterThanOrEqual(3);
    });

    test('应该能够通过关键词搜索节点', () => {
      const searchResults = nodeRegistryService.searchNodes('粒子');
      expect(searchResults.length).toBeGreaterThan(0);
      
      const particleResults = searchResults.filter(node => 
        node.type.includes('particles/')
      );
      expect(particleResults.length).toBeGreaterThanOrEqual(15);
    });
  });

  describe('节点统计测试', () => {
    test('应该正确统计第8批次节点数量', () => {
      const stats = nodeRegistryService.getNodeStatistics();
      expect(stats.totalNodes).toBeGreaterThanOrEqual(147); // 117个现有 + 30个新节点
    });

    test('应该包含第8批次的标签统计', () => {
      const stats = nodeRegistryService.getNodeStatistics();
      expect(stats.tagCounts['粒子']).toBeGreaterThanOrEqual(15);
      expect(stats.tagCounts['场景']).toBeGreaterThanOrEqual(5);
      expect(stats.tagCounts['地形']).toBeGreaterThanOrEqual(7);
      expect(stats.tagCounts['水体']).toBeGreaterThanOrEqual(3);
    });
  });

  describe('节点完整性测试', () => {
    test('所有第8批次节点都应该有必要的属性', () => {
      const allNodes = nodeRegistryService.getAllNodes();
      const batch8Nodes = allNodes.filter(node =>
        node.type.startsWith('scene/skybox/') ||
        node.type.startsWith('scene/fog/') ||
        node.type.startsWith('scene/environment/') ||
        node.type.startsWith('particles/') ||
        node.type.startsWith('terrain/') ||
        node.type.startsWith('water/')
      );

      batch8Nodes.forEach(node => {
        expect(node.type).toBeDefined();
        expect(node.label).toBeDefined();
        expect(node.description).toBeDefined();
        expect(node.category).toBeDefined();
        expect(node.color).toBeDefined();
        expect(node.tags).toBeDefined();
        expect(Array.isArray(node.tags)).toBe(true);
        expect(node.tags.length).toBeGreaterThan(0);
      });
    });

    test('节点类型应该遵循命名规范', () => {
      const allNodes = nodeRegistryService.getAllNodes();
      const batch8Nodes = allNodes.filter(node => 
        node.type.startsWith('scene/skybox/') ||
        node.type.startsWith('scene/fog/') ||
        node.type.startsWith('scene/environment/') ||
        node.type.startsWith('particles/') ||
        node.type.startsWith('terrain/')
      );

      batch8Nodes.forEach(node => {
        // 检查类型格式：category/subcategory/action
        const parts = node.type.split('/');
        expect(parts.length).toBeGreaterThanOrEqual(2);
        expect(parts[0]).toMatch(/^[a-z]+$/); // 第一部分应该是小写字母
      });
    });
  });

  describe('性能测试', () => {
    test('节点注册性能应该在可接受范围内', () => {
      const startTime = performance.now();
      
      // 模拟重新注册所有第8批次节点
      const batch8NodeTypes = [
        'scene/skybox/setSkybox',
        'scene/fog/enableFog',
        'particles/system/createParticleSystem',
        'particles/emitter/createEmitter',
        'terrain/generation/createTerrain'
      ];

      batch8NodeTypes.forEach(nodeType => {
        const node = nodeRegistryService.getNode(nodeType);
        expect(node).toBeDefined();
      });

      const endTime = performance.now();
      const duration = endTime - startTime;
      
      // 注册和查询应该在10ms内完成
      expect(duration).toBeLessThan(10);
    });

    test('节点搜索性能应该在可接受范围内', () => {
      const startTime = performance.now();
      
      const searchResults = nodeRegistryService.searchNodes('粒子');
      expect(searchResults.length).toBeGreaterThan(0);
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      // 搜索应该在5ms内完成
      expect(duration).toBeLessThan(5);
    });
  });
});
