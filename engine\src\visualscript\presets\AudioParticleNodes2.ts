/**
 * 音频与粒子系统节点 - 第二部分
 * 第8批次：音频与粒子系统（节点219-240）
 * 包含剩余的粒子系统节点
 */

import * as THREE from 'three';
import type { Entity } from '../../core/Entity';
import { Node } from '../nodes/Node';
import { NodeCategory, SocketDirection, SocketType } from '../nodes/Node';

// ============================================================================
// 粒子系统节点（219-240）- 继续
// ============================================================================

/**
 * 设置发射形状节点 (219)
 * 设置粒子发射形状
 */
export class SetEmissionShapeNode extends Node {
  constructor() {
    super('particles/emitter/setEmissionShape', '设置发射形状', NodeCategory.ENTITY);

    this.addInput({
      name: 'emitter',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '粒子发射器'
    });

    this.addInput({
      name: 'shape',
      type: SocketType.STRING,
      direction: SocketDirection.INPUT,
      description: '发射形状',
      defaultValue: 'point'
    });

    this.addInput({
      name: 'size',
      type: SocketType.NUMBER,
      direction: SocketDirection.INPUT,
      description: '形状大小',
      defaultValue: 1
    });

    this.addOutput({
      name: 'emitter',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      description: '更新后的发射器'
    });
  }

  public execute(): any {
    const emitter = this.getInputValue('emitter');
    const shape = this.getInputValue('shape');
    const size = this.getInputValue('size');

    if (!emitter) {
      console.warn('SetEmissionShapeNode: 发射器为空');
      this.setOutputValue('emitter', null);
      this.triggerFlow('completed');
      return;
    }

    try {
      emitter.shape = shape;
      emitter.size = size;
      this.setOutputValue('emitter', emitter);
      this.triggerFlow('completed');
    } catch (error) {
      console.error('SetEmissionShapeNode: 设置发射形状失败', error);
      this.setOutputValue('emitter', emitter);
      this.triggerFlow('completed');
    }
  }
}

/**
 * 设置粒子寿命节点 (220)
 * 设置粒子存活时间
 */
export class SetLifetimeNode extends Node {
  constructor() {
    super('particles/particle/setLifetime', '设置粒子寿命', NodeCategory.ENTITY);

    this.addInput({
      name: 'particleSystem',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '粒子系统'
    });

    this.addInput({
      name: 'lifetime',
      type: SocketType.NUMBER,
      direction: SocketDirection.INPUT,
      description: '粒子寿命（秒）',
      defaultValue: 5.0
    });

    this.addOutput({
      name: 'particleSystem',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      description: '更新后的粒子系统'
    });
  }

  public execute(): any {
    const particleSystem = this.getInputValue('particleSystem');
    const lifetime = this.getInputValue('lifetime');

    if (!particleSystem) {
      console.warn('SetLifetimeNode: 粒子系统为空');
      this.setOutputValue('particleSystem', null);
      this.triggerFlow('completed');
      return;
    }

    try {
      (particleSystem as any).particleLifetime = lifetime;
      this.setOutputValue('particleSystem', particleSystem);
      this.triggerFlow('completed');
    } catch (error) {
      console.error('SetLifetimeNode: 设置粒子寿命失败', error);
      this.setOutputValue('particleSystem', particleSystem);
      this.triggerFlow('completed');
    }
  }
}

/**
 * 设置粒子速度节点 (221)
 * 设置粒子初始速度
 */
export class SetVelocityNode extends Node {
  constructor() {
    super('particles/particle/setVelocity', '设置粒子速度', NodeCategory.ENTITY);

    this.addInput({
      name: 'particleSystem',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '粒子系统'
    });

    this.addInput({
      name: 'velocity',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '粒子速度',
      defaultValue: new THREE.Vector3(0, 1, 0)
    });

    this.addInput({
      name: 'randomness',
      type: SocketType.NUMBER,
      direction: SocketDirection.INPUT,
      description: '随机性',
      defaultValue: 0.5
    });

    this.addOutput({
      name: 'particleSystem',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      description: '更新后的粒子系统'
    });
  }

  public execute(): any {
    const particleSystem = this.getInputValue('particleSystem');
    const velocity = this.getInputValue('velocity');
    const randomness = this.getInputValue('randomness');

    if (!particleSystem) {
      console.warn('SetVelocityNode: 粒子系统为空');
      this.setOutputValue('particleSystem', null);
      this.triggerFlow('completed');
      return;
    }

    try {
      (particleSystem as any).particleVelocity = velocity.clone();
      (particleSystem as any).velocityRandomness = randomness;
      this.setOutputValue('particleSystem', particleSystem);
      this.triggerFlow('completed');
    } catch (error) {
      console.error('SetVelocityNode: 设置粒子速度失败', error);
      this.setOutputValue('particleSystem', particleSystem);
      this.triggerFlow('completed');
    }
  }
}

/**
 * 设置粒子大小节点 (222)
 * 设置粒子尺寸
 */
export class SetSizeNode extends Node {
  constructor() {
    super('particles/particle/setSize', '设置粒子大小', NodeCategory.ENTITY);

    this.addInput({
      name: 'particleSystem',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '粒子系统'
    });

    this.addInput({
      name: 'size',
      type: SocketType.NUMBER,
      direction: SocketDirection.INPUT,
      description: '粒子大小',
      defaultValue: 1.0
    });

    this.addInput({
      name: 'sizeVariation',
      type: SocketType.NUMBER,
      direction: SocketDirection.INPUT,
      description: '大小变化',
      defaultValue: 0.5
    });

    this.addOutput({
      name: 'particleSystem',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      description: '更新后的粒子系统'
    });
  }

  public execute(): any {
    const particleSystem = this.getInputValue('particleSystem');
    const size = this.getInputValue('size');
    const sizeVariation = this.getInputValue('sizeVariation');

    if (!particleSystem) {
      console.warn('SetSizeNode: 粒子系统为空');
      this.setOutputValue('particleSystem', null);
      this.triggerFlow('completed');
      return;
    }

    try {
      const material = particleSystem.material as THREE.PointsMaterial;
      material.size = size;
      (particleSystem as any).sizeVariation = sizeVariation;
      this.setOutputValue('particleSystem', particleSystem);
      this.triggerFlow('completed');
    } catch (error) {
      console.error('SetSizeNode: 设置粒子大小失败', error);
      this.setOutputValue('particleSystem', particleSystem);
      this.triggerFlow('completed');
    }
  }
}

/**
 * 设置粒子颜色节点 (223)
 * 设置粒子颜色
 */
export class SetColorNode extends Node {
  constructor() {
    super('particles/particle/setColor', '设置粒子颜色', NodeCategory.ENTITY);

    this.addInput({
      name: 'particleSystem',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '粒子系统'
    });

    this.addInput({
      name: 'color',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '粒子颜色',
      defaultValue: 0xffffff
    });

    this.addOutput({
      name: 'particleSystem',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      description: '更新后的粒子系统'
    });
  }

  public execute(): any {
    const particleSystem = this.getInputValue('particleSystem');
    const color = this.getInputValue('color');

    if (!particleSystem) {
      console.warn('SetColorNode: 粒子系统为空');
      this.setOutputValue('particleSystem', null);
      this.triggerFlow('completed');
      return;
    }

    try {
      const material = particleSystem.material as THREE.PointsMaterial;
      material.color.setHex(color);
      (particleSystem as any).particleColor = color;
      this.setOutputValue('particleSystem', particleSystem);
      this.triggerFlow('completed');
    } catch (error) {
      console.error('SetColorNode: 设置粒子颜色失败', error);
      this.setOutputValue('particleSystem', particleSystem);
      this.triggerFlow('completed');
    }
  }
}

/**
 * 添加重力节点 (224)
 * 为粒子添加重力影响
 */
export class AddGravityNode extends Node {
  constructor() {
    super('particles/forces/addGravity', '添加重力', NodeCategory.ENTITY);

    this.addInput({
      name: 'particleSystem',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '粒子系统'
    });

    this.addInput({
      name: 'gravity',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '重力向量',
      defaultValue: new THREE.Vector3(0, -9.8, 0)
    });

    this.addOutput({
      name: 'particleSystem',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      description: '更新后的粒子系统'
    });
  }

  public execute(): any {
    const particleSystem = this.getInputValue('particleSystem');
    const gravity = this.getInputValue('gravity');

    if (!particleSystem) {
      console.warn('AddGravityNode: 粒子系统为空');
      this.setOutputValue('particleSystem', null);
      this.triggerFlow('completed');
      return;
    }

    try {
      (particleSystem as any).gravity = gravity.clone();
      this.setOutputValue('particleSystem', particleSystem);
      this.triggerFlow('completed');
    } catch (error) {
      console.error('AddGravityNode: 添加重力失败', error);
      this.setOutputValue('particleSystem', particleSystem);
      this.triggerFlow('completed');
    }
  }
}

/**
 * 添加风力节点 (225)
 * 为粒子添加风力影响
 */
export class AddWindNode extends Node {
  constructor() {
    super('particles/forces/addWind', '添加风力', NodeCategory.ENTITY);

    this.addInput({
      name: 'particleSystem',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '粒子系统'
    });

    this.addInput({
      name: 'windForce',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '风力向量',
      defaultValue: new THREE.Vector3(1, 0, 0)
    });

    this.addInput({
      name: 'strength',
      type: SocketType.NUMBER,
      direction: SocketDirection.INPUT,
      description: '风力强度',
      defaultValue: 1.0
    });

    this.addOutput({
      name: 'particleSystem',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      description: '更新后的粒子系统'
    });
  }

  public execute(): any {
    const particleSystem = this.getInputValue('particleSystem');
    const windForce = this.getInputValue('windForce');
    const strength = this.getInputValue('strength');

    if (!particleSystem) {
      console.warn('AddWindNode: 粒子系统为空');
      this.setOutputValue('particleSystem', null);
      this.triggerFlow('completed');
      return;
    }

    try {
      const wind = windForce.clone().multiplyScalar(strength);
      (particleSystem as any).wind = wind;
      this.setOutputValue('particleSystem', particleSystem);
      this.triggerFlow('completed');
    } catch (error) {
      console.error('AddWindNode: 添加风力失败', error);
      this.setOutputValue('particleSystem', particleSystem);
      this.triggerFlow('completed');
    }
  }
}

/**
 * 添加湍流节点 (226)
 * 为粒子添加湍流效果
 */
export class AddTurbulenceNode extends Node {
  constructor() {
    super('particles/forces/addTurbulence', '添加湍流', NodeCategory.ENTITY);

    this.addInput({
      name: 'particleSystem',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '粒子系统'
    });

    this.addInput({
      name: 'strength',
      type: SocketType.NUMBER,
      direction: SocketDirection.INPUT,
      description: '湍流强度',
      defaultValue: 1.0
    });

    this.addInput({
      name: 'frequency',
      type: SocketType.NUMBER,
      direction: SocketDirection.INPUT,
      description: '湍流频率',
      defaultValue: 0.1
    });

    this.addOutput({
      name: 'particleSystem',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      description: '更新后的粒子系统'
    });
  }

  public execute(): any {
    const particleSystem = this.getInputValue('particleSystem');
    const strength = this.getInputValue('strength');
    const frequency = this.getInputValue('frequency');

    if (!particleSystem) {
      console.warn('AddTurbulenceNode: 粒子系统为空');
      this.setOutputValue('particleSystem', null);
      this.triggerFlow('completed');
      return;
    }

    try {
      (particleSystem as any).turbulence = {
        strength: strength,
        frequency: frequency
      };
      this.setOutputValue('particleSystem', particleSystem);
      this.triggerFlow('completed');
    } catch (error) {
      console.error('AddTurbulenceNode: 添加湍流失败', error);
      this.setOutputValue('particleSystem', particleSystem);
      this.triggerFlow('completed');
    }
  }
}

/**
 * 启用粒子碰撞节点 (227)
 * 启用粒子与物体碰撞
 */
export class EnableCollisionNode extends Node {
  constructor() {
    super('particles/collision/enableCollision', '启用粒子碰撞', NodeCategory.ENTITY);

    this.addInput({
      name: 'particleSystem',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '粒子系统'
    });

    this.addInput({
      name: 'colliders',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '碰撞体数组'
    });

    this.addInput({
      name: 'bounce',
      type: SocketType.NUMBER,
      direction: SocketDirection.INPUT,
      description: '弹性系数',
      defaultValue: 0.5
    });

    this.addOutput({
      name: 'particleSystem',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      description: '更新后的粒子系统'
    });
  }

  public execute(): any {
    const particleSystem = this.getInputValue('particleSystem');
    const colliders = this.getInputValue('colliders');
    const bounce = this.getInputValue('bounce');

    if (!particleSystem) {
      console.warn('EnableCollisionNode: 粒子系统为空');
      this.setOutputValue('particleSystem', null);
      this.triggerFlow('completed');
      return;
    }

    try {
      (particleSystem as any).collision = {
        enabled: true,
        colliders: colliders || [],
        bounce: bounce
      };
      this.setOutputValue('particleSystem', particleSystem);
      this.triggerFlow('completed');
    } catch (error) {
      console.error('EnableCollisionNode: 启用粒子碰撞失败', error);
      this.setOutputValue('particleSystem', particleSystem);
      this.triggerFlow('completed');
    }
  }
}
