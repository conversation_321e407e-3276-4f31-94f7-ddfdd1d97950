/**
 * 音频与粒子系统节点 - 第四部分
 * 第8批次：音频与粒子系统（节点236-240）
 * 包含最后的地形和水体系统节点
 */

import * as THREE from 'three';
import type { Entity } from '../../core/Entity';
import { Node } from '../nodes/Node';
import { NodeCategory, SocketDirection, SocketType } from '../nodes/Node';

// ============================================================================
// 地形和水体系统节点（236-240）- 最后部分
// ============================================================================

/**
 * 启用地形LOD节点 (236)
 * 启用地形细节层次
 */
export class EnableTerrainLODNode extends Node {
  constructor() {
    super('terrain/lod/enableTerrainLOD', '启用地形LOD', NodeCategory.ENTITY);

    this.addInput({
      name: 'terrain',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '地形对象'
    });

    this.addInput({
      name: 'lodLevels',
      type: SocketType.NUMBER,
      direction: SocketDirection.INPUT,
      description: 'LOD级别数',
      defaultValue: 3
    });

    this.addInput({
      name: 'distance',
      type: SocketType.NUMBER,
      direction: SocketDirection.INPUT,
      description: 'LOD切换距离',
      defaultValue: 100
    });

    this.addOutput({
      name: 'terrain',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      description: '更新后的地形'
    });
  }

  public execute(): any {
    const terrain = this.getInputValue('terrain');
    const lodLevels = this.getInputValue('lodLevels');
    const distance = this.getInputValue('distance');

    if (!terrain) {
      console.warn('EnableTerrainLODNode: 地形为空');
      this.setOutputValue('terrain', null);
      this.triggerFlow('completed');
      return;
    }

    try {
      // 添加LOD属性到地形对象
      (terrain as any).lodEnabled = true;
      (terrain as any).lodLevels = lodLevels;
      (terrain as any).lodDistance = distance;

      this.setOutputValue('terrain', terrain);
      this.triggerFlow('completed');
    } catch (error) {
      console.error('EnableTerrainLODNode: 启用地形LOD失败', error);
      this.setOutputValue('terrain', terrain);
      this.triggerFlow('completed');
    }
  }
}

/**
 * 启用地形碰撞节点 (237)
 * 启用地形物理碰撞
 */
export class EnableTerrainCollisionNode extends Node {
  constructor() {
    super('terrain/collision/enableTerrainCollision', '启用地形碰撞', NodeCategory.ENTITY);

    this.addInput({
      name: 'terrain',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '地形对象'
    });

    this.addInput({
      name: 'collisionType',
      type: SocketType.STRING,
      direction: SocketDirection.INPUT,
      description: '碰撞类型',
      defaultValue: 'mesh'
    });

    this.addOutput({
      name: 'terrain',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      description: '更新后的地形'
    });
  }

  public execute(): any {
    const terrain = this.getInputValue('terrain');
    const collisionType = this.getInputValue('collisionType');

    if (!terrain) {
      console.warn('EnableTerrainCollisionNode: 地形为空');
      this.setOutputValue('terrain', null);
      this.triggerFlow('completed');
      return;
    }

    try {
      // 添加碰撞属性到地形对象
      (terrain as any).collisionEnabled = true;
      (terrain as any).collisionType = collisionType;

      this.setOutputValue('terrain', terrain);
      this.triggerFlow('completed');
    } catch (error) {
      console.error('EnableTerrainCollisionNode: 启用地形碰撞失败', error);
      this.setOutputValue('terrain', terrain);
      this.triggerFlow('completed');
    }
  }
}

/**
 * 创建水面节点 (238)
 * 创建水体表面
 */
export class CreateWaterSurfaceNode extends Node {
  constructor() {
    super('water/system/createWaterSurface', '创建水面', NodeCategory.ENTITY);

    this.addInput({
      name: 'width',
      type: SocketType.NUMBER,
      direction: SocketDirection.INPUT,
      description: '水面宽度',
      defaultValue: 100
    });

    this.addInput({
      name: 'height',
      type: SocketType.NUMBER,
      direction: SocketDirection.INPUT,
      description: '水面高度',
      defaultValue: 100
    });

    this.addInput({
      name: 'color',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '水面颜色',
      defaultValue: 0x006994
    });

    this.addOutput({
      name: 'waterSurface',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      description: '水面对象'
    });
  }

  public execute(): any {
    const width = this.getInputValue('width');
    const height = this.getInputValue('height');
    const color = this.getInputValue('color');

    try {
      // 创建水面几何体
      const geometry = new THREE.PlaneGeometry(width, height, 32, 32);
      
      // 创建水面材质
      const material = new THREE.MeshLambertMaterial({
        color: color,
        transparent: true,
        opacity: 0.7
      });

      // 创建水面网格
      const waterSurface = new THREE.Mesh(geometry, material);
      waterSurface.rotation.x = -Math.PI / 2; // 水平放置

      // 添加水面属性
      (waterSurface as any).isWater = true;
      (waterSurface as any).waveHeight = 0.5;
      (waterSurface as any).waveSpeed = 1.0;

      this.setOutputValue('waterSurface', waterSurface);
      this.triggerFlow('completed');
    } catch (error) {
      console.error('CreateWaterSurfaceNode: 创建水面失败', error);
      this.setOutputValue('waterSurface', null);
      this.triggerFlow('completed');
    }
  }
}

/**
 * 添加波浪节点 (239)
 * 为水面添加波浪效果
 */
export class AddWavesNode extends Node {
  constructor() {
    super('water/waves/addWaves', '添加波浪', NodeCategory.ENTITY);

    this.addInput({
      name: 'waterSurface',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '水面对象'
    });

    this.addInput({
      name: 'waveHeight',
      type: SocketType.NUMBER,
      direction: SocketDirection.INPUT,
      description: '波浪高度',
      defaultValue: 1.0
    });

    this.addInput({
      name: 'waveSpeed',
      type: SocketType.NUMBER,
      direction: SocketDirection.INPUT,
      description: '波浪速度',
      defaultValue: 1.0
    });

    this.addInput({
      name: 'waveFrequency',
      type: SocketType.NUMBER,
      direction: SocketDirection.INPUT,
      description: '波浪频率',
      defaultValue: 0.1
    });

    this.addOutput({
      name: 'waterSurface',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      description: '更新后的水面'
    });
  }

  public execute(): any {
    const waterSurface = this.getInputValue('waterSurface');
    const waveHeight = this.getInputValue('waveHeight');
    const waveSpeed = this.getInputValue('waveSpeed');
    const waveFrequency = this.getInputValue('waveFrequency');

    if (!waterSurface) {
      console.warn('AddWavesNode: 水面为空');
      this.setOutputValue('waterSurface', null);
      this.triggerFlow('completed');
      return;
    }

    try {
      // 添加波浪属性
      (waterSurface as any).waveHeight = waveHeight;
      (waterSurface as any).waveSpeed = waveSpeed;
      (waterSurface as any).waveFrequency = waveFrequency;
      (waterSurface as any).hasWaves = true;

      this.setOutputValue('waterSurface', waterSurface);
      this.triggerFlow('completed');
    } catch (error) {
      console.error('AddWavesNode: 添加波浪失败', error);
      this.setOutputValue('waterSurface', waterSurface);
      this.triggerFlow('completed');
    }
  }
}

/**
 * 启用水面反射节点 (240)
 * 启用水面反射效果
 */
export class EnableReflectionNode extends Node {
  constructor() {
    super('water/reflection/enableReflection', '启用水面反射', NodeCategory.ENTITY);

    this.addInput({
      name: 'waterSurface',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '水面对象'
    });

    this.addInput({
      name: 'reflectionIntensity',
      type: SocketType.NUMBER,
      direction: SocketDirection.INPUT,
      description: '反射强度',
      defaultValue: 0.8
    });

    this.addInput({
      name: 'reflectionQuality',
      type: SocketType.STRING,
      direction: SocketDirection.INPUT,
      description: '反射质量',
      defaultValue: 'medium'
    });

    this.addOutput({
      name: 'waterSurface',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      description: '更新后的水面'
    });
  }

  public execute(): any {
    const waterSurface = this.getInputValue('waterSurface');
    const reflectionIntensity = this.getInputValue('reflectionIntensity');
    const reflectionQuality = this.getInputValue('reflectionQuality');

    if (!waterSurface) {
      console.warn('EnableReflectionNode: 水面为空');
      this.setOutputValue('waterSurface', null);
      this.triggerFlow('completed');
      return;
    }

    try {
      // 添加反射属性
      (waterSurface as any).reflectionEnabled = true;
      (waterSurface as any).reflectionIntensity = reflectionIntensity;
      (waterSurface as any).reflectionQuality = reflectionQuality;

      // 更新材质以支持反射
      const material = waterSurface.material as THREE.MeshLambertMaterial;
      material.transparent = true;
      material.opacity = 0.8;
      material.needsUpdate = true;

      this.setOutputValue('waterSurface', waterSurface);
      this.triggerFlow('completed');
    } catch (error) {
      console.error('EnableReflectionNode: 启用水面反射失败', error);
      this.setOutputValue('waterSurface', waterSurface);
      this.triggerFlow('completed');
    }
  }
}
