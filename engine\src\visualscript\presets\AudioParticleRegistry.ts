/**
 * 音频与粒子系统节点注册
 * 第8批次：音频与粒子系统（节点211-240）
 */

import { NodeRegistry } from '../nodes/NodeRegistry';
import { NodeCategory } from '../nodes/Node';

// 导入高级音频节点
import {
  SetSkyboxNode,
  EnableFogNode,
  SetFogColorNode,
  SetFogDensityNode,
  SetEnvironmentMapNode,
  CreateParticleSystemNode,
  CreateEmitterNode,
  SetEmissionRateNode
} from './AudioParticleNodes';

// 导入粒子系统节点
import {
  SetEmissionShapeNode,
  SetLifetimeNode,
  SetVelocityNode,
  SetSizeNode,
  SetColorNode,
  AddGravityNode,
  AddWindNode,
  AddTurbulenceNode,
  EnableCollisionNode
} from './AudioParticleNodes2';

// 导入剩余节点
import {
  SetParticleMaterialNode,
  AnimateSizeNode,
  AnimateColorNode,
  CreateTerrainNode,
  GenerateHeightmapNode,
  ApplyNoiseNode,
  SetTerrainTextureNode,
  BlendTexturesNode
} from './AudioParticleNodes3';

// 导入最后的节点
import {
  EnableTerrainLODNode,
  EnableTerrainCollisionNode,
  CreateWaterSurfaceNode,
  AddWavesNode,
  EnableReflectionNode
} from './AudioParticleNodes4';

/**
 * 注册第8批次音频与粒子系统节点
 * @param registry 节点注册表
 */
export function registerAudioParticleNodes(registry: NodeRegistry): void {
  // ============================================================================
  // 高级音频节点（211-215）- 场景环境音频
  // ============================================================================

  // 注册设置天空盒节点 (211)
  registry.registerNodeType({
    type: 'scene/skybox/setSkybox',
    category: NodeCategory.ENTITY,
    constructor: SetSkyboxNode,
    label: '设置天空盒',
    description: '设置场景天空盒',
    icon: 'skybox',
    color: '#87CEEB',
    tags: ['场景', '天空盒', '环境']
  });

  // 注册启用雾效节点 (212)
  registry.registerNodeType({
    type: 'scene/fog/enableFog',
    category: NodeCategory.ENTITY,
    constructor: EnableFogNode,
    label: '启用雾效',
    description: '启用场景雾效果',
    icon: 'fog',
    color: '#B0C4DE',
    tags: ['场景', '雾效', '视觉效果']
  });

  // 注册设置雾颜色节点 (213)
  registry.registerNodeType({
    type: 'scene/fog/setFogColor',
    category: NodeCategory.ENTITY,
    constructor: SetFogColorNode,
    label: '设置雾颜色',
    description: '设置雾的颜色',
    icon: 'color-palette',
    color: '#B0C4DE',
    tags: ['场景', '雾效', '颜色']
  });

  // 注册设置雾密度节点 (214)
  registry.registerNodeType({
    type: 'scene/fog/setFogDensity',
    category: NodeCategory.ENTITY,
    constructor: SetFogDensityNode,
    label: '设置雾密度',
    description: '设置雾的浓度',
    icon: 'density',
    color: '#B0C4DE',
    tags: ['场景', '雾效', '密度']
  });

  // 注册设置环境贴图节点 (215)
  registry.registerNodeType({
    type: 'scene/environment/setEnvironmentMap',
    category: NodeCategory.ENTITY,
    constructor: SetEnvironmentMapNode,
    label: '设置环境贴图',
    description: '设置IBL环境贴图',
    icon: 'environment',
    color: '#32CD32',
    tags: ['场景', '环境', 'IBL']
  });

  // ============================================================================
  // 粒子系统节点（216-230）
  // ============================================================================

  // 注册创建粒子系统节点 (216)
  registry.registerNodeType({
    type: 'particles/system/createParticleSystem',
    category: NodeCategory.ENTITY,
    constructor: CreateParticleSystemNode,
    label: '创建粒子系统',
    description: '创建粒子效果系统',
    icon: 'particles',
    color: '#FF6347',
    tags: ['粒子', '特效', '系统']
  });

  // 注册创建发射器节点 (217)
  registry.registerNodeType({
    type: 'particles/emitter/createEmitter',
    category: NodeCategory.ENTITY,
    constructor: CreateEmitterNode,
    label: '创建发射器',
    description: '创建粒子发射器',
    icon: 'emitter',
    color: '#FF6347',
    tags: ['粒子', '发射器', '特效']
  });

  // 注册设置发射速率节点 (218)
  registry.registerNodeType({
    type: 'particles/emitter/setEmissionRate',
    category: NodeCategory.ENTITY,
    constructor: SetEmissionRateNode,
    label: '设置发射速率',
    description: '设置粒子发射频率',
    icon: 'rate',
    color: '#FF6347',
    tags: ['粒子', '发射', '速率']
  });

  // 注册设置发射形状节点 (219)
  registry.registerNodeType({
    type: 'particles/emitter/setEmissionShape',
    category: NodeCategory.ENTITY,
    constructor: SetEmissionShapeNode,
    label: '设置发射形状',
    description: '设置粒子发射形状',
    icon: 'shape',
    color: '#FF6347',
    tags: ['粒子', '发射', '形状']
  });

  // 注册设置粒子寿命节点 (220)
  registry.registerNodeType({
    type: 'particles/particle/setLifetime',
    category: NodeCategory.ENTITY,
    constructor: SetLifetimeNode,
    label: '设置粒子寿命',
    description: '设置粒子存活时间',
    icon: 'lifetime',
    color: '#FFA500',
    tags: ['粒子', '寿命', '时间']
  });

  // 注册设置粒子速度节点 (221)
  registry.registerNodeType({
    type: 'particles/particle/setVelocity',
    category: NodeCategory.ENTITY,
    constructor: SetVelocityNode,
    label: '设置粒子速度',
    description: '设置粒子初始速度',
    icon: 'velocity',
    color: '#FFA500',
    tags: ['粒子', '速度', '运动']
  });

  // 注册设置粒子大小节点 (222)
  registry.registerNodeType({
    type: 'particles/particle/setSize',
    category: NodeCategory.ENTITY,
    constructor: SetSizeNode,
    label: '设置粒子大小',
    description: '设置粒子尺寸',
    icon: 'size',
    color: '#FFA500',
    tags: ['粒子', '大小', '尺寸']
  });

  // 注册设置粒子颜色节点 (223)
  registry.registerNodeType({
    type: 'particles/particle/setColor',
    category: NodeCategory.ENTITY,
    constructor: SetColorNode,
    label: '设置粒子颜色',
    description: '设置粒子颜色',
    icon: 'color',
    color: '#FFA500',
    tags: ['粒子', '颜色', '外观']
  });

  // 注册添加重力节点 (224)
  registry.registerNodeType({
    type: 'particles/forces/addGravity',
    category: NodeCategory.ENTITY,
    constructor: AddGravityNode,
    label: '添加重力',
    description: '为粒子添加重力影响',
    icon: 'gravity',
    color: '#8A2BE2',
    tags: ['粒子', '重力', '物理']
  });

  // 注册添加风力节点 (225)
  registry.registerNodeType({
    type: 'particles/forces/addWind',
    category: NodeCategory.ENTITY,
    constructor: AddWindNode,
    label: '添加风力',
    description: '为粒子添加风力影响',
    icon: 'wind',
    color: '#8A2BE2',
    tags: ['粒子', '风力', '物理']
  });

  // 注册添加湍流节点 (226)
  registry.registerNodeType({
    type: 'particles/forces/addTurbulence',
    category: NodeCategory.ENTITY,
    constructor: AddTurbulenceNode,
    label: '添加湍流',
    description: '为粒子添加湍流效果',
    icon: 'turbulence',
    color: '#8A2BE2',
    tags: ['粒子', '湍流', '物理']
  });

  // 注册启用粒子碰撞节点 (227)
  registry.registerNodeType({
    type: 'particles/collision/enableCollision',
    category: NodeCategory.ENTITY,
    constructor: EnableCollisionNode,
    label: '启用粒子碰撞',
    description: '启用粒子与物体碰撞',
    icon: 'collision',
    color: '#DC143C',
    tags: ['粒子', '碰撞', '物理']
  });

  // 注册设置粒子材质节点 (228)
  registry.registerNodeType({
    type: 'particles/material/setParticleMaterial',
    category: NodeCategory.ENTITY,
    constructor: SetParticleMaterialNode,
    label: '设置粒子材质',
    description: '设置粒子渲染材质',
    icon: 'material',
    color: '#4169E1',
    tags: ['粒子', '材质', '渲染']
  });

  // 注册动画粒子大小节点 (229)
  registry.registerNodeType({
    type: 'particles/animation/animateSize',
    category: NodeCategory.ANIMATION,
    constructor: AnimateSizeNode,
    label: '动画粒子大小',
    description: '创建粒子大小动画',
    icon: 'animate-size',
    color: '#FF1493',
    tags: ['粒子', '动画', '大小']
  });

  // 注册动画粒子颜色节点 (230)
  registry.registerNodeType({
    type: 'particles/animation/animateColor',
    category: NodeCategory.ANIMATION,
    constructor: AnimateColorNode,
    label: '动画粒子颜色',
    description: '创建粒子颜色动画',
    icon: 'animate-color',
    color: '#FF1493',
    tags: ['粒子', '动画', '颜色']
  });

  // ============================================================================
  // 地形系统节点（231-232）- 部分实现
  // ============================================================================

  // 注册创建地形节点 (231)
  registry.registerNodeType({
    type: 'terrain/generation/createTerrain',
    category: NodeCategory.ENTITY,
    constructor: CreateTerrainNode,
    label: '创建地形',
    description: '创建地形网格',
    icon: 'terrain',
    color: '#8B4513',
    tags: ['地形', '生成', '网格']
  });

  // 注册生成高度图节点 (232)
  registry.registerNodeType({
    type: 'terrain/generation/generateHeightmap',
    category: NodeCategory.ENTITY,
    constructor: GenerateHeightmapNode,
    label: '生成高度图',
    description: '生成地形高度图',
    icon: 'heightmap',
    color: '#8B4513',
    tags: ['地形', '高度图', '生成']
  });

  // 注册应用噪声节点 (233)
  registry.registerNodeType({
    type: 'terrain/generation/applyNoise',
    category: NodeCategory.ENTITY,
    constructor: ApplyNoiseNode,
    label: '应用噪声',
    description: '为地形应用噪声纹理',
    icon: 'noise',
    color: '#8B4513',
    tags: ['地形', '噪声', '生成']
  });

  // 注册设置地形纹理节点 (234)
  registry.registerNodeType({
    type: 'terrain/texture/setTerrainTexture',
    category: NodeCategory.ENTITY,
    constructor: SetTerrainTextureNode,
    label: '设置地形纹理',
    description: '设置地形表面纹理',
    icon: 'texture',
    color: '#8B4513',
    tags: ['地形', '纹理', '外观']
  });

  // 注册混合纹理节点 (235)
  registry.registerNodeType({
    type: 'terrain/texture/blendTextures',
    category: NodeCategory.ENTITY,
    constructor: BlendTexturesNode,
    label: '混合纹理',
    description: '混合多个地形纹理',
    icon: 'blend',
    color: '#8B4513',
    tags: ['地形', '纹理', '混合']
  });

  // 注册启用地形LOD节点 (236)
  registry.registerNodeType({
    type: 'terrain/lod/enableTerrainLOD',
    category: NodeCategory.ENTITY,
    constructor: EnableTerrainLODNode,
    label: '启用地形LOD',
    description: '启用地形细节层次',
    icon: 'lod',
    color: '#8B4513',
    tags: ['地形', 'LOD', '优化']
  });

  // 注册启用地形碰撞节点 (237)
  registry.registerNodeType({
    type: 'terrain/collision/enableTerrainCollision',
    category: NodeCategory.ENTITY,
    constructor: EnableTerrainCollisionNode,
    label: '启用地形碰撞',
    description: '启用地形物理碰撞',
    icon: 'collision',
    color: '#8B4513',
    tags: ['地形', '碰撞', '物理']
  });

  // 注册创建水面节点 (238)
  registry.registerNodeType({
    type: 'water/system/createWaterSurface',
    category: NodeCategory.ENTITY,
    constructor: CreateWaterSurfaceNode,
    label: '创建水面',
    description: '创建水体表面',
    icon: 'water',
    color: '#006994',
    tags: ['水体', '创建', '表面']
  });

  // 注册添加波浪节点 (239)
  registry.registerNodeType({
    type: 'water/waves/addWaves',
    category: NodeCategory.ENTITY,
    constructor: AddWavesNode,
    label: '添加波浪',
    description: '为水面添加波浪效果',
    icon: 'waves',
    color: '#006994',
    tags: ['水体', '波浪', '效果']
  });

  // 注册启用水面反射节点 (240)
  registry.registerNodeType({
    type: 'water/reflection/enableReflection',
    category: NodeCategory.ENTITY,
    constructor: EnableReflectionNode,
    label: '启用水面反射',
    description: '启用水面反射效果',
    icon: 'reflection',
    color: '#006994',
    tags: ['水体', '反射', '视觉效果']
  });

  console.log('第8批次音频与粒子系统节点注册完成：30个节点（211-240）');
}
