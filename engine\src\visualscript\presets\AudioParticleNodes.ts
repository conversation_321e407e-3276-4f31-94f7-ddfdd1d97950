/**
 * 音频与粒子系统节点
 * 第8批次：音频与粒子系统（节点211-240）
 * 包含高级音频节点和粒子系统节点
 */

import * as THREE from 'three';
import type { Entity } from '../../core/Entity';
import { Node } from '../nodes/Node';
import { NodeCategory, SocketDirection, SocketType } from '../nodes/Node';

// ============================================================================
// 高级音频节点（211-215）- 场景环境音频
// ============================================================================

/**
 * 设置天空盒节点 (211)
 * 设置场景天空盒
 */
export class SetSkyboxNode extends Node {
  constructor() {
    super('scene/skybox/setSkybox', '设置天空盒', NodeCategory.ENTITY);

    this.addInput({
      name: 'scene',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '场景对象'
    });

    this.addInput({
      name: 'skyboxTexture',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '天空盒纹理'
    });

    this.addOutput({
      name: 'scene',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      description: '更新后的场景'
    });
  }

  public execute(): any {
    const scene = this.getInputValue('scene');
    const skyboxTexture = this.getInputValue('skyboxTexture');

    if (!scene) {
      console.warn('SetSkyboxNode: 场景为空');
      this.setOutputValue('scene', null);
      this.triggerFlow('completed');
      return;
    }

    try {
      if (skyboxTexture) {
        // 设置天空盒
        scene.background = skyboxTexture;
        scene.environment = skyboxTexture;
      } else {
        // 创建默认天空盒
        const loader = new THREE.CubeTextureLoader();
        const skybox = loader.load([
          'textures/skybox/px.jpg', 'textures/skybox/nx.jpg',
          'textures/skybox/py.jpg', 'textures/skybox/ny.jpg',
          'textures/skybox/pz.jpg', 'textures/skybox/nz.jpg'
        ]);
        scene.background = skybox;
        scene.environment = skybox;
      }

      this.setOutputValue('scene', scene);
      this.triggerFlow('completed');
    } catch (error) {
      console.error('SetSkyboxNode: 设置天空盒失败', error);
      this.setOutputValue('scene', scene);
      this.triggerFlow('completed');
    }
  }
}

/**
 * 启用雾效节点 (212)
 * 启用场景雾效果
 */
export class EnableFogNode extends Node {
  constructor() {
    super('scene/fog/enableFog', '启用雾效', NodeCategory.ENTITY);

    this.addInput({
      name: 'scene',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '场景对象'
    });

    this.addInput({
      name: 'color',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '雾颜色',
      defaultValue: 0xcccccc
    });

    this.addInput({
      name: 'near',
      type: SocketType.NUMBER,
      direction: SocketDirection.INPUT,
      description: '近距离',
      defaultValue: 1
    });

    this.addInput({
      name: 'far',
      type: SocketType.NUMBER,
      direction: SocketDirection.INPUT,
      description: '远距离',
      defaultValue: 1000
    });

    this.addOutput({
      name: 'scene',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      description: '更新后的场景'
    });
  }

  public execute(): any {
    const scene = this.getInputValue('scene');
    const color = this.getInputValue('color');
    const near = this.getInputValue('near');
    const far = this.getInputValue('far');

    if (!scene) {
      console.warn('EnableFogNode: 场景为空');
      this.setOutputValue('scene', null);
      this.triggerFlow('completed');
      return;
    }

    try {
      // 启用雾效
      scene.fog = new THREE.Fog(color, near, far);

      this.setOutputValue('scene', scene);
      this.triggerFlow('completed');
    } catch (error) {
      console.error('EnableFogNode: 启用雾效失败', error);
      this.setOutputValue('scene', scene);
      this.triggerFlow('completed');
    }
  }
}

/**
 * 设置雾颜色节点 (213)
 * 设置雾的颜色
 */
export class SetFogColorNode extends Node {
  constructor() {
    super('scene/fog/setFogColor', '设置雾颜色', NodeCategory.ENTITY);

    this.addInput({
      name: 'scene',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '场景对象'
    });

    this.addInput({
      name: 'color',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '雾颜色',
      defaultValue: 0xcccccc
    });

    this.addOutput({
      name: 'scene',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      description: '更新后的场景'
    });
  }

  public execute(): any {
    const scene = this.getInputValue('scene');
    const color = this.getInputValue('color');

    if (!scene) {
      console.warn('SetFogColorNode: 场景为空');
      this.setOutputValue('scene', null);
      this.triggerFlow('completed');
      return;
    }

    try {
      if (scene.fog) {
        scene.fog.color.setHex(color);
      } else {
        // 如果没有雾效，创建一个
        scene.fog = new THREE.Fog(color, 1, 1000);
      }

      this.setOutputValue('scene', scene);
      this.triggerFlow('completed');
    } catch (error) {
      console.error('SetFogColorNode: 设置雾颜色失败', error);
      this.setOutputValue('scene', scene);
      this.triggerFlow('completed');
    }
  }
}

/**
 * 设置雾密度节点 (214)
 * 设置雾的浓度
 */
export class SetFogDensityNode extends Node {
  constructor() {
    super('scene/fog/setFogDensity', '设置雾密度', NodeCategory.ENTITY);

    this.addInput({
      name: 'scene',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '场景对象'
    });

    this.addInput({
      name: 'density',
      type: SocketType.NUMBER,
      direction: SocketDirection.INPUT,
      description: '雾密度',
      defaultValue: 0.00025
    });

    this.addOutput({
      name: 'scene',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      description: '更新后的场景'
    });
  }

  public execute(): any {
    const scene = this.getInputValue('scene');
    const density = this.getInputValue('density');

    if (!scene) {
      console.warn('SetFogDensityNode: 场景为空');
      this.setOutputValue('scene', null);
      this.triggerFlow('completed');
      return;
    }

    try {
      // 使用指数雾
      scene.fog = new THREE.FogExp2(scene.fog?.color || 0xcccccc, density);

      this.setOutputValue('scene', scene);
      this.triggerFlow('completed');
    } catch (error) {
      console.error('SetFogDensityNode: 设置雾密度失败', error);
      this.setOutputValue('scene', scene);
      this.triggerFlow('completed');
    }
  }
}

/**
 * 设置环境贴图节点 (215)
 * 设置IBL环境贴图
 */
export class SetEnvironmentMapNode extends Node {
  constructor() {
    super('scene/environment/setEnvironmentMap', '设置环境贴图', NodeCategory.ENTITY);

    this.addInput({
      name: 'scene',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '场景对象'
    });

    this.addInput({
      name: 'environmentMap',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '环境贴图'
    });

    this.addInput({
      name: 'intensity',
      type: SocketType.NUMBER,
      direction: SocketDirection.INPUT,
      description: '环境光强度',
      defaultValue: 1.0
    });

    this.addOutput({
      name: 'scene',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      description: '更新后的场景'
    });
  }

  public execute(): any {
    const scene = this.getInputValue('scene');
    const environmentMap = this.getInputValue('environmentMap');
    const intensity = this.getInputValue('intensity');

    if (!scene) {
      console.warn('SetEnvironmentMapNode: 场景为空');
      this.setOutputValue('scene', null);
      this.triggerFlow('completed');
      return;
    }

    try {
      if (environmentMap) {
        scene.environment = environmentMap;
        scene.environmentIntensity = intensity;
      }

      this.setOutputValue('scene', scene);
      this.triggerFlow('completed');
    } catch (error) {
      console.error('SetEnvironmentMapNode: 设置环境贴图失败', error);
      this.setOutputValue('scene', scene);
      this.triggerFlow('completed');
    }
  }
}

// ============================================================================
// 粒子系统节点（216-230）
// ============================================================================

/**
 * 创建粒子系统节点 (216)
 * 创建粒子效果系统
 */
export class CreateParticleSystemNode extends Node {
  constructor() {
    super('particles/system/createParticleSystem', '创建粒子系统', NodeCategory.ENTITY);

    this.addInput({
      name: 'maxParticles',
      type: SocketType.NUMBER,
      direction: SocketDirection.INPUT,
      description: '最大粒子数',
      defaultValue: 1000
    });

    this.addOutput({
      name: 'particleSystem',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      description: '粒子系统'
    });
  }

  public execute(): any {
    const maxParticles = this.getInputValue('maxParticles');

    try {
      // 创建粒子系统
      const geometry = new THREE.BufferGeometry();
      const positions = new Float32Array(maxParticles * 3);
      const colors = new Float32Array(maxParticles * 3);
      const sizes = new Float32Array(maxParticles);

      geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
      geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));
      geometry.setAttribute('size', new THREE.BufferAttribute(sizes, 1));

      const material = new THREE.PointsMaterial({
        size: 1,
        vertexColors: true,
        transparent: true,
        opacity: 0.8
      });

      const particleSystem = new THREE.Points(geometry, material);

      // 添加粒子系统属性
      (particleSystem as any).maxParticles = maxParticles;
      (particleSystem as any).particleCount = 0;
      (particleSystem as any).particles = [];

      this.setOutputValue('particleSystem', particleSystem);
      this.triggerFlow('completed');
    } catch (error) {
      console.error('CreateParticleSystemNode: 创建粒子系统失败', error);
      this.setOutputValue('particleSystem', null);
      this.triggerFlow('completed');
    }
  }
}

/**
 * 创建发射器节点 (217)
 * 创建粒子发射器
 */
export class CreateEmitterNode extends Node {
  constructor() {
    super('particles/emitter/createEmitter', '创建发射器', NodeCategory.ENTITY);

    this.addInput({
      name: 'position',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '发射器位置',
      defaultValue: new THREE.Vector3(0, 0, 0)
    });

    this.addInput({
      name: 'rate',
      type: SocketType.NUMBER,
      direction: SocketDirection.INPUT,
      description: '发射速率',
      defaultValue: 10
    });

    this.addOutput({
      name: 'emitter',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      description: '粒子发射器'
    });
  }

  public execute(): any {
    const position = this.getInputValue('position');
    const rate = this.getInputValue('rate');

    try {
      // 创建发射器
      const emitter = {
        position: position.clone(),
        rate: rate,
        shape: 'point',
        direction: new THREE.Vector3(0, 1, 0),
        spread: Math.PI / 6,
        speed: 5,
        lastEmitTime: 0
      };

      this.setOutputValue('emitter', emitter);
      this.triggerFlow('completed');
    } catch (error) {
      console.error('CreateEmitterNode: 创建发射器失败', error);
      this.setOutputValue('emitter', null);
      this.triggerFlow('completed');
    }
  }
}

/**
 * 设置发射速率节点 (218)
 * 设置粒子发射频率
 */
export class SetEmissionRateNode extends Node {
  constructor() {
    super('particles/emitter/setEmissionRate', '设置发射速率', NodeCategory.ENTITY);

    this.addInput({
      name: 'emitter',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      description: '粒子发射器'
    });

    this.addInput({
      name: 'rate',
      type: SocketType.NUMBER,
      direction: SocketDirection.INPUT,
      description: '发射速率',
      defaultValue: 10
    });

    this.addOutput({
      name: 'emitter',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      description: '更新后的发射器'
    });
  }

  public execute(): any {
    const emitter = this.getInputValue('emitter');
    const rate = this.getInputValue('rate');

    if (!emitter) {
      console.warn('SetEmissionRateNode: 发射器为空');
      this.setOutputValue('emitter', null);
      this.triggerFlow('completed');
      return;
    }

    try {
      emitter.rate = rate;
      this.setOutputValue('emitter', emitter);
      this.triggerFlow('completed');
    } catch (error) {
      console.error('SetEmissionRateNode: 设置发射速率失败', error);
      this.setOutputValue('emitter', emitter);
      this.triggerFlow('completed');
    }
  }
}
